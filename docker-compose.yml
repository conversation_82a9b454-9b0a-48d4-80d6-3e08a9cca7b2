version: '3.3'

services:
  web:
    image: php56-local
    build: .docker
    volumes:
    - ./:/var/www/html
  #  - ./.docker/apache/sites-enabled:/etc/apache2/sites-enabled
  #  - ./.docker/php/xdebug.ini:/usr/local/etc/php/conf.d/xdebug.ini
    ports:
    - 80:80
    - 443:443
    environment:
      PHP_IDE_CONFIG: serverName=telcall.cz
      DOCKER_DEV: 'true'
      PHP_XDEBUG_ENABLED: 1
      XDEBUG_CONFIG: "remote_host=**********"
    links:
      - database
      #   - blackfire
      #- elasticsearch

    # blackfire:
    #  image: blackfire/blackfire
      #environment:
      # Exposes BLACKFIRE_SERVER_* environment variables from the host
      #  BLACKFIRE_SERVER_ID: 658fb813-a29a-4376-ab8c-7eb8edb74e67
    #   BLACKFIRE_SERVER_TOKEN: 79a4462da28100c2ac2019ba2bb341879ced7df0c414565972bea87f3e71cde5

    #docker-compose exec web env XDEBUG_CONFIG=remote_host=************



  database:
    image: mariadb:10.5.15
    volumes:
      - ./.docker/database:/docker-entrypoint-initdb.d:cached,ro
    ports:
      - 3308:3306
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: mobilbarcz2
      MYSQL_PASSWORD: mobilbarcz2
      MYSQL_DATABASE: mobilbarcz2
    command: mysqld --sql_mode="NO_ENGINE_SUBSTITUTION"

  #phpmyadmin:
  #  image: phpmyadmin/phpmyadmin
  #  ports:
  #  - 8081:80
  #  environment:
  #    PMA_HOSTS: database
  #    UPLOAD_LIMIT: 100M
  #  links:
  #    - database

 # composer:
  #  image: composer:latest
  #  volumes:
  #  - ./:/app
  #  working_dir: /app
  #  command: composer install --ignore-platform-reqs

    #elasticsearch:
    # build: .docker/elasticsearch
    # container_name: elasticsearch
      # volumes:
    #  - .docker/elasticsearch/hunspell:/usr/share/elasticsearch/config/hunspell
      # healthcheck:
      #  interval: 30s
      #  timeout: 50s
    #   retries: 3

    #kibana:
    # build: .docker/kibana
    # container_name: kibana
      #links:
    #  - elasticsearch
      #depends_on:
    #  - elasticsearch
      #ports:
      # - 5601:5601

    #cerebro:
    #  image: lmenezes/cerebro
    # container_name: cerebro
      # links:
    #   - elasticsearch
      #depends_on:
    #  - elasticsearch
      # ports:
    #  - 9000:9000

    #redis:
    #image: redis:alpine


