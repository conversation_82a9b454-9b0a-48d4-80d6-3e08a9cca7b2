# Makefile pro TelCall.cz Docker prostředí
# Použití: make [příkaz]

# Proměnné
DOCKER_COMPOSE = docker-compose
PROJECT_NAME = telcallcz
WEB_CONTAINER = $(PROJECT_NAME)_web_1
DB_CONTAINER = $(PROJECT_NAME)_database_1

# Barvy pro výstup
GREEN = \033[0;32m
YELLOW = \033[1;33m
RED = \033[0;31m
NC = \033[0m # No Color

.PHONY: help build up down restart logs shell db-shell clean status ps install composer-install

# Výchozí příkaz - zobraz<PERSON> nápovědu
help:
	@echo "$(GREEN)TelCall.cz Docker Makefile$(NC)"
	@echo ""
	@echo "$(YELLOW)Dostupné příkazy:$(NC)"
	@echo "  $(GREEN)build$(NC)           - Sestaví Docker images"
	@echo "  $(GREEN)up$(NC)              - Spustí všechny kontejnery"
	@echo "  $(GREEN)down$(NC)            - Zastaví a odstraní kontejnery"
	@echo "  $(GREEN)restart$(NC)         - Restartuje všechny kontejnery"
	@echo "  $(GREEN)logs$(NC)            - Zobrazí logy všech kontejnerů"
	@echo "  $(GREEN)logs-web$(NC)        - Zobrazí logy web kontejneru"
	@echo "  $(GREEN)logs-db$(NC)         - Zobrazí logy databáze"
	@echo "  $(GREEN)shell$(NC)           - Připojí se do web kontejneru (bash)"
	@echo "  $(GREEN)db-shell$(NC)        - Připojí se do databáze (mysql)"
	@echo "  $(GREEN)status$(NC)          - Zobrazí stav kontejnerů"
	@echo "  $(GREEN)ps$(NC)              - Zobrazí běžící kontejnery"
	@echo "  $(GREEN)clean$(NC)           - Vyčistí nepoužívané Docker objekty"
	@echo "  $(GREEN)install$(NC)         - Kompletní instalace (build + up)"
	@echo "  $(GREEN)composer-install$(NC) - Spustí composer install"
	@echo "  $(GREEN)backup-db$(NC)       - Vytvoří zálohu databáze"
	@echo "  $(GREEN)restore-db$(NC)      - Obnoví databázi ze zálohy"

# Sestaví Docker images
build:
	@echo "$(YELLOW)Sestavuji Docker images...$(NC)"
	$(DOCKER_COMPOSE) build

# Spustí všechny kontejnery
up:
	@echo "$(YELLOW)Spouštím kontejnery...$(NC)"
	$(DOCKER_COMPOSE) up -d
	@echo "$(GREEN)Kontejnery spuštěny!$(NC)"
	@echo "$(GREEN)Web aplikace: http://localhost$(NC)"
	@echo "$(GREEN)Databáze: localhost:3308$(NC)"

# Zastaví a odstraní kontejnery
down:
	@echo "$(YELLOW)Zastavuji kontejnery...$(NC)"
	$(DOCKER_COMPOSE) down
	@echo "$(GREEN)Kontejnery zastaveny!$(NC)"

# Restartuje všechny kontejnery
restart: down up

# Zobrazí logy všech kontejnerů
logs:
	$(DOCKER_COMPOSE) logs -f

# Zobrazí logy web kontejneru
logs-web:
	$(DOCKER_COMPOSE) logs -f web

# Zobrazí logy databáze
logs-db:
	$(DOCKER_COMPOSE) logs -f database

# Připojí se do web kontejneru
shell:
	@echo "$(YELLOW)Připojuji se do web kontejneru...$(NC)"
	$(DOCKER_COMPOSE) exec web bash

# Připojí se do databáze
db-shell:
	@echo "$(YELLOW)Připojuji se do databáze...$(NC)"
	$(DOCKER_COMPOSE) exec database mysql -u mobilbarcz2 -pmobilbarcz2 mobilbarcz2

# Zobrazí stav kontejnerů
status:
	@echo "$(YELLOW)Stav kontejnerů:$(NC)"
	$(DOCKER_COMPOSE) ps

# Alias pro status
ps: status

# Vyčistí nepoužívané Docker objekty
clean:
	@echo "$(YELLOW)Čistím nepoužívané Docker objekty...$(NC)"
	docker system prune -f
	docker volume prune -f
	@echo "$(GREEN)Vyčištěno!$(NC)"

# Kompletní instalace
install: build up
	@echo "$(GREEN)Instalace dokončena!$(NC)"

# Spustí composer install
composer-install:
	@echo "$(YELLOW)Spouštím composer install...$(NC)"
	$(DOCKER_COMPOSE) exec web composer install --ignore-platform-reqs
	@echo "$(GREEN)Composer install dokončen!$(NC)"

# Vytvoří zálohu databáze
backup-db:
	@echo "$(YELLOW)Vytvářím zálohu databáze...$(NC)"
	@mkdir -p backups
	$(DOCKER_COMPOSE) exec database mysqldump -u mobilbarcz2 -pmobilbarcz2 mobilbarcz2 > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)Záloha vytvořena v adresáři backups/$(NC)"

# Obnoví databázi ze zálohy (použití: make restore-db BACKUP=backup_file.sql)
restore-db:
	@if [ -z "$(BACKUP)" ]; then \
		echo "$(RED)Chyba: Musíte zadat soubor zálohy. Použití: make restore-db BACKUP=backup_file.sql$(NC)"; \
		exit 1; \
	fi
	@echo "$(YELLOW)Obnovuji databázi ze zálohy $(BACKUP)...$(NC)"
	$(DOCKER_COMPOSE) exec -T database mysql -u mobilbarcz2 -pmobilbarcz2 mobilbarcz2 < $(BACKUP)
	@echo "$(GREEN)Databáze obnovena!$(NC)"

# Zobrazí informace o prostředí
info:
	@echo "$(GREEN)=== TelCall.cz Docker prostředí ===$(NC)"
	@echo "$(YELLOW)Web aplikace:$(NC) http://localhost"
	@echo "$(YELLOW)Databáze:$(NC) localhost:3308"
	@echo "$(YELLOW)Uživatel DB:$(NC) mobilbarcz2"
	@echo "$(YELLOW)Heslo DB:$(NC) mobilbarcz2"
	@echo "$(YELLOW)Název DB:$(NC) mobilbarcz2"
	@echo ""
	@echo "$(YELLOW)Užitečné příkazy:$(NC)"
	@echo "  make up      - Spustit prostředí"
	@echo "  make down    - Zastavit prostředí"
	@echo "  make shell   - Připojit se do kontejneru"
	@echo "  make logs    - Zobrazit logy"

# Rychlé příkazy
start: up
stop: down
rebuild: down build up
