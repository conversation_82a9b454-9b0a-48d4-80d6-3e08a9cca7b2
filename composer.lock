{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "f8e2ac3af19191ed39f35f635da864ad", "packages": [{"name": "mpdf/mpdf", "version": "v6.1.4", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "e7310ce53b5c11db1f0b781fe0a69066608ac6e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/e7310ce53b5c11db1f0b781fe0a69066608ac6e2", "reference": "e7310ce53b5c11db1f0b781fe0a69066608ac6e2", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^5.4.0 || 7.0.*", "setasign/fpdi": "1.6.*"}, "require-dev": {"phpunit/phpunit": "^4.7"}, "suggest": {"ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"classmap": ["mpdf.php", "classes"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0"], "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "A PHP class to generate PDF files from HTML with Unicode/UTF-8 and CJK support", "homepage": "http://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "time": "2017-07-09T16:46:12+00:00"}, {"name": "robrichards/wse-php", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/robrichards/wse-php.git", "reference": "7d2218937008e8badac5d080309bab76431fbc61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/robrichards/wse-php/zipball/7d2218937008e8badac5d080309bab76431fbc61", "reference": "7d2218937008e8badac5d080309bab76431fbc61", "shasum": ""}, "require": {"php": ">= 5.3", "robrichards/xmlseclibs": ">=2.0"}, "type": "library", "autoload": {"psr-4": {"RobRichards\\WsePhp\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://www.cdatazone.org/", "role": "Main developer"}], "description": "Libraries for adding WS-* support to ext/soap in PHP.", "homepage": "https://github.com/robrichards/wse-php", "keywords": ["WS-Security", "soap", "ws-addressing"], "time": "2017-08-31T09:48:18+00:00"}, {"name": "robrichards/xmlseclibs", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/robrichards/xmlseclibs.git", "reference": "118450a141ac2336be1b5e5e91a22229441b0277"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/robrichards/xmlseclibs/zipball/118450a141ac2336be1b5e5e91a22229441b0277", "reference": "118450a141ac2336be1b5e5e91a22229441b0277", "shasum": ""}, "require": {"php": ">= 5.3"}, "suggest": {"ext-mcrypt": "MCrypt extension", "ext-openssl": "OpenSSL extension"}, "type": "library", "autoload": {"psr-4": {"RobRichards\\XMLSecLibs\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "A PHP library for XML Security", "homepage": "https://github.com/robrichards/xmlseclibs", "keywords": ["security", "signature", "xml", "xmldsig"], "time": "2019-11-05T11:51:00+00:00"}, {"name": "setasign/fpdi", "version": "1.6.2", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "a6ad58897a6d97cc2d2cd2adaeda343b25a368ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/a6ad58897a6d97cc2d2cd2adaeda343b25a368ea", "reference": "a6ad58897a6d97cc2d2cd2adaeda343b25a368ea", "shasum": ""}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use \"tecnickcom/tcpdf\" as an alternative there's no fixed dependency configured.", "setasign/fpdi-fpdf": "Use this package to automatically evaluate dependencies to FPDF.", "setasign/fpdi-tcpdf": "Use this package to automatically evaluate dependencies to TCPDF."}, "type": "library", "autoload": {"classmap": ["filters/", "fpdi.php", "fpdf_tpl.php", "fpdi_pdf_parser.php", "pdf_context.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "time": "2017-05-11T14:25:49+00:00"}, {"name": "vyfakturuj/vyfakturuj-api-php", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/redbitcz/vyfakturuj-api-php.git", "reference": "93d8131257ad260dc83c90e7904d58adae51e6eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/redbitcz/vyfakturuj-api-php/zipball/93d8131257ad260dc83c90e7904d58adae51e6eb", "reference": "93d8131257ad260dc83c90e7904d58adae51e6eb", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=5.6.0"}, "suggest": {"composer/ca-bundle": "Umožňuje ověřit SSL certifikáty serveru"}, "type": "library", "autoload": {"classmap": ["libs/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Redbit s.r.o.", "homepage": "https://www.redbit.cz/"}], "description": "Vyfakturuj API", "homepage": "https://www.vyfakturuj.cz/api/", "keywords": ["api", "faktura", "vy<PERSON><PERSON><PERSON><PERSON>"], "time": "2020-05-13T11:28:43+00:00"}, {"name": "zdenekgebauer/eet", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/zdenekgebauer/eet.git", "reference": "052032b96df43e4935c7344a8b497f1f8c67e25d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zdenekgebauer/eet/zipball/052032b96df43e4935c7344a8b497f1f8c67e25d", "reference": "052032b96df43e4935c7344a8b497f1f8c67e25d", "shasum": ""}, "require": {"ext-curl": "*", "ext-openssl": "*", "ext-soap": "*", "php": ">=5.3.0", "robrichards/wse-php": "2.0.*", "robrichards/xmlseclibs": "^2.0"}, "type": "library", "autoload": {"psr-4": {"ZdenekGebauer\\Eet\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["WTFPL"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "EET client (www.etrzby.cz)", "homepage": "https://github.com/zdenekgebauer/eet", "time": "2017-03-04T13:20:23+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {"zdenekgebauer/eet": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "platform-overrides": {"php": "5.6"}, "plugin-api-version": "1.1.0"}