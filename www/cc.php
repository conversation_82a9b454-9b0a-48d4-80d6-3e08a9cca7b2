<?php
/**
 * mazání cache
 */

/**
 * vym<PERSON><PERSON><PERSON> ad<PERSON> v<PERSON>etně obsahu
 *
 * @param $dir
 */
function rrmdir($dir, $deleteDir = TRUE) {
  if (is_dir($dir)) {
    $objects = scandir($dir);
    foreach ($objects as $object) {
      if ($object != "." && $object != "..") {
        if (filetype($dir . "/" . $object) == "dir") {
          rrmdir($dir . "/" . $object);
        } else {
          unlink($dir . "/" . $object);
        }
      }
    }
    reset($objects);
    if ($deleteDir) rmdir($dir);
  }
}

if ($_GET["k"] == 'lZwJIL') {
  rrmdir(__DIR__ . "/../temp/cache/", FALSE);
  unlink(__DIR__ . "/../temp/btfj.dat");
  echo "Cache vymazana ...";
}
