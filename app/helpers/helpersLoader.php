<?php

class TemplateFilters extends Nette\Object {
    /** @var string */
    private $wwwDir;

    /** @var Nette\Application\UI\Presenter */
    private $presenter;

    /** @var int */
    private $formatPriceDecimals=0;

    /** @var int */
    private $formatPriceCurrency='Kč';

    public function __construct($wwwDir, Nette\Application\Application $application) {
      $this->wwwDir = $wwwDir;
      $this->presenter = $application->getPresenter();
      if ($this->presenter !== NULL) {
        $this->formatPriceCurrency = (string)$this->presenter->currency["code"];
        $this->formatPriceDecimals = (int)$this->presenter->currency["decimals"];
      }
    }

    /**
    * Method we will register as callback
    * in method $template->addFilter().
    */
    public function loader($helper) {
      if (method_exists($this, $helper)) {
        //return [$this, $helper];
      }
      return call_user_func_array(callback($this, $helper), array_slice(func_get_args(), 1));
    }
  /**
  * vraci src obrazku katalogu
  * @return string cesta k obrazku
  */
  public function formatPhoneNumer($value) {
    $value = substr($value, -9);
    $value = substr($value, 0, 3)." ".substr($value, 3, 3)." ".substr($value, 6, 3);
    return $value; 
  }

  public function getPriceNoVat($priceVat, $vatid=0, $precision=0) {
    $koef[0] = 0.1736; //zakladni
    $koef[1] = 0.1304; //snizena
    $koef[2] = 0.0909; //treti

    $vat = $priceVat * $koef[$vatid];
    return round($priceVat - $vat, $precision);
  }

  /**
  * vraci onlina status na zaklade poctu minut necinnosti
  * @return string cesta k obrazku
  */
  public function getOnlineStatus($logOffMin) {
    $cache = \Nette\Environment::getCache('app');
    $config = $cache["config"];
    if ($logOffMin < $config["LOGIN_DURATION"]) {
      return('online');
    }
    $logOffMin = $logOffMin / 60;
    if (24 < $config["LOGIN_DURATION"]) {
      return('offline méně než 24 hodin');
    }
    if (48 < $config["LOGIN_DURATION"]) {
      return('offline méně než 48 hodin');
    }
    return('offline déle než 48 hodin');
  }
}  
?>