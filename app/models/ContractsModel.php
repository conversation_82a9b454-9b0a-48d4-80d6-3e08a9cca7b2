<?php
namespace Model;
  
class ContractsModel extends BaseModel {
  
  protected $tableName = "contracts";
  protected $fieldPrefix = "con";
  
  public function getCode($usrid, $colid) {
    return ($usrid.substr('0000'.$colid, -4));
  }
  
  /********************* ciselniky *********************/
  /**
  * ciselnik pagtype
  * @return array
  */
  public function getEnumConType() {
    return array(
      'contract' => 'Smlouva',
      'add' => 'Dodatek',
    );
  }
  
  public function getEnumConMasId() {
    return \dibi::fetchPairs("SELECT conid, conname FROM contracts WHERE contype='contract' ORDER BY conid");
  }
}
?>