<?php
namespace Model;
use dibi;
  
class InvoicesModel extends BaseModel {
  
  protected $tableName = "invoices";
  protected $fieldPrefix = "inv";

  public function delete($id) {
    //vymazu polozky
    $rows = dibi::fetchAll("SELECT iniid FROM invoiceitems WHERE iniinvid=%i", $id);
    $inis = new \Model\InvoiceitemsModel();
    foreach ($rows as $row) {
      $inis->delete($row->iniid);
    }
    return parent::delete($id);
  }

  public function checkPayed ($varSym, $value, $date, $invid=0) {
    if ($invid > 0) {
      $invoice = dibi::fetch("SELECT * FROM invoices WHERE invstatus=0 AND invid=%i", $invid);
    } else {
      $invoice = dibi::fetch("SELECT * FROM invoices WHERE invstatus=0 AND invvarsym=%s", $varSym);
    }  
    if (empty($date)) $date = new \DateTime;
    if ($invoice) {
      if ((double)$invoice->invpricevat==(double)$value) {
        //nasel jsem uhradu - nastavim fa jako uhrazenou
        $vals = array(
          'invstatus'=> 1,
          'invpayeddate'=> $date,
        );
        if (!empty($invoice->invpayeddate)) unset($vals["invpayeddate"]);
        $ret = $this->update($invoice->invid, $vals);
        return ($ret);
      } else {
        return true;
      }
    }
    return false;  
  }
  
  /**
  * Vraci nasledujici kod faktury
  * 
  * @param int $year - pro jaky rok
  * @param string $sec - sekce [wifi,call,othr]
  */
  Public function getNextInvCode ($year, $sec) {
    switch ($sec) {
      case "call":
       $secId = 1;
       break;
      case "wifi":
       $secId = 9;
       break;
      case "othr":
       $secId = 8;
       break;
    }
    $code = (string)dibi::fetchSingle("SELECT invcode FROM invoices WHERE invtype=%s", $sec, " AND invyear=%i", $year, " ORDER BY invcode DESC");
    if (empty($code)) {
      //tento rok jeste neni zadna fa
      $nextId = 1;
    } else {
      $nextId = (int)substr($code, -4) + 1;
    }
    return (substr($year, -2).$secId.substr('000'.$nextId, -4));
  }

  /**
   * Vraci nasledujici variabilní symbol
   *
   * @param int $year - pro jaky rok
   */
  Public function getNextVarSym ($year) {
    $code = (string)dibi::fetchSingle("SELECT invvarsym FROM invoices WHERE invyear=%i", $year, " ORDER BY invvarsym DESC");
    if (empty($code)) {
      //tento rok jeste neni zadny varsym
      $nextId = 1;
    } else {
      $nextId = (int)substr($code, -4) + 1;
    }
    return ($year.substr('000'.$nextId, -4));
  }

  Public function getPayValue ($invoice) {
    $invPayVal = $invoice->invpricevat;
    if (!empty($invoice->invpayeddate)) $invPayVal = 0;
    //splatky
    $val = (double)dibi::fetchSingle("SELECT SUM(invpricevat) FROM invoices WHERE invinvid=%i", $invoice->invid, " AND invpayeddate IS NOT NULL");
    $invPayVal = $invPayVal - $val;
    return($invPayVal);
  }
  
    
  /********************* ciselniky *********************/ 
  /**
  * ciselnik invstatus
  * @return array
  */
  public function getEnumInvStatus() {
    return array(
      0 => 'Neuhrazena',
      1 => 'Uhrazena',
      2 => 'Stornována',
    );
  }
  /**
  * ciselnik zpusob uhrady
  * @return array
  */
  public function getEnumPayTypId() {
    return array(
      1 => 'Převodem',
      2 => 'Hotově',
      3 => 'Inkasem',
    );
  }
  
  public function getEnumPayRateId($sec) {
    if ($sec == 'wifi') {
      return array(
        0 => 'Nefakturovat',
        1 => 'Měsíčně',
      );
    } else {
      return array(
        0 => 'Nefakturovat',
        1 => 'Měsíčně',
        2 => 'Kvartálně',
        3 => 'Půlročně',
        4 => 'Ročně',
      );
    }
  }
  
  public function getEnumSendType() {
    return array(
      0 => 'Neposílat',
      1 => 'Emailem',
      2 => 'SMS',
    );
  }
  
  public function getEnumInvType() {
    return array(
      'call' => 'Volání',
      'wifi' => 'Internet',
      'othr' => 'Ostatní',
    );
  }
  
}


?>