<?php
namespace Model;
use dibi;
  
class AdminsModel extends BaseModel {
  
  protected $tableName = "admins";
  protected $fieldPrefix = "adm";
  
  /********************* ciselniky *********************/
  
  /**
  * ciselnik admstatus
  * @return array
  */
  public function getEnumAdmStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }
  
  /**
  * ciselnik admrole
  * @return array
  */
  public function getEnumAdmRole() {
    return array(
      'superadmin' => 'Superadmin',
      'editor' => 'Editor',
    );
  }
  
  public function getEnumAdmId() {
    $items = dibi::query("SELECT * FROM admins ORDER BY admname")
      ->fetchPairs('admid', 'admname');    
    return $items;  
  }  
}
?>