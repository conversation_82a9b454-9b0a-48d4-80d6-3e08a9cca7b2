<?php
namespace Model;
use dibi;

class ModelException extends \Exception { }

abstract class BaseModel extends \Nette\Object {
  //identifikatory vlastnosti datoveho pole  
  /** @var string nazev tabulky */
  protected $tableName;
  
  /** @var string nazev tabulky s prefixem */
  private $table;
  
  /** @var string prefix datoveho pole */
  protected $fieldPrefix;

  public function __construct() {
    $this->table = $this->tableName;
  }

  /**
  * vraci vlastnosti sloupce
  * 
  * @param string $colName nazev sloupce
  * @return array
  */
  public function getColProperties($colName) {
    return Null;
  }
  
  /**
  * vraci pro dany sloupec hodnotu prislusne property
  * 
  * @param string $colName nazev sloupce
  * @param string $colProperty nazev property [type|size|nullable|default]
  * @return string 
  */
  public function getColProperty($colName, $colProperty) {
    return Null;
  }
  
  /**
  * nastavi proslusnemu sloupci hodnotu prislusne property
  * 
  * @param string $colName nazev sloupce
  * @param string $colProperty nazev property [type|size|nullable|default]
  * @param string $propertyValue hodnota property
  * @return boolean
  */
  protected function setColProperty($colName, $colProperty, $propertyValue) {
    return false;   
  }
  
  public function getDataSource($sql="") {
    if ($sql == "") $sql = "SELECT * FROM $this->table";
    return dibi::dataSource($sql);
  }

  /**
  * vraci jeden zaznam 
  * 
  * @param integer $id hodnota id ve smyslu jednoznacneho identifikatoru, nemusi byt primarni klic
  * @param string $col nazev sloupce bez prefixu
  * @return dibiRow 
  */
  public function load($id, $col='id') {
    $colName = $this->fieldPrefix.$col;
    $result = $this->getDataSource()
      ->where($colName.'=%'.(is_numeric($id) ? 'i' : 's'), $id)
      ->applyLimit(1)
      ->getResult();
    return $result->fetch();
  }
  
  public function update($id, $data, $setDateU = True) {
    if ($setDateU) $data[$this->fieldPrefix.'dateu'] = new \DateTime;
    return dibi::update($this->table, $data)
      ->where($this->fieldPrefix.'id=%i', $id)
      ->execute();
  }

  public function insert($data) {
    $data[$this->fieldPrefix.'datec'] = new \DateTime;
    return dibi::insert($this->table, $data)
      ->execute(dibi::IDENTIFIER);
  }
  
  public function save(&$id, $data, $setDateU = True) {
    if ($id > 0) {
      return $this->update($id, $data, $setDateU);
    } else {
      $id = $this->insert($data);
      return($id > 0);
    }
  }

  public function delete($id) {
    return dibi::delete($this->table)
      ->where($this->fieldPrefix.'id=%i', $id)
      ->execute();
  }
  
  public function fetchAll($sql) {
    $result = dibi::dataSource($sql)
      ->getResult();
    return $result->fetchAll(); 
  }
  
  /**
   * Computes salted password hash.
   * @param  string
   * @return string
   */
  static function calculateHash($password) {
    //return md5($password . str_repeat('Hczg^,!RpYeNPZ`;L$a19%c\^/@X=AHW', 10));
    return md5($password);
  }
  
  /**
  * generuje nahodne heslo
  * 
  * @param string $length
  * @return string
  */
  static function genPassword($length = 6) {
    $base = "abcdefghjkmnpqrstwxyz123456789";
    $max = strlen($base)-1;
    $string = "";
    mt_srand((double)microtime()*1000000);
    while (strlen($string) < $length) $string .= $base[mt_rand(0,$max)];
    return $string;
  }
  
  /********************* ciselniky *********************/
  /**
  * ciselnik názvu měsíců
  * @return array
  */
  public function getEnumMonId() {
    return array(
      1 => 'leden',
      2 => 'únor',
      3 => 'březen',
      4 => 'duben',
      5 => 'květen',
      6 => 'červen',
      7 => 'červenec',
      8 => 'srpen',
      9 => 'září',
      10 => 'říjen',
      11 => 'listopad',
      12 => 'prosinec',
    );
  }

  /**
  * ciselnik admstatus
  * @return array
  */
  public function getEnumSerId() {
    return array(
      'tmobile' => 'T-Mobile',
      'tmobile2' => 'T-Mobile 2',
      'o2' => 'O2',
      'vodafone' => 'Vodafone',
    );
  }

}