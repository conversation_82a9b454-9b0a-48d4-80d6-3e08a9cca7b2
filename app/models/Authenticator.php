<?php

/**
 * Users and <PERSON><PERSON> authenticator.
 */
class Authenticator extends Nette\Object implements Nette\Security\IAuthenticator {
  /** namespace admin */
  const NS_ADMIN = 'admin';
  /** namespace user */
  const NS_USER  = 'user';
  
  /**
   * Performs an authentication
   * @param  array
   * @return Nette\Security\Identity
   * @throws Nette\Security\AuthenticationException
   */
  public function authenticate(array $credentials) {
    list($username, $password, $namespace) = $credentials;
    if (empty($namespace)) throw new Nette\Security\AuthenticationException("Špatné volání authentizace.", self::IDENTITY_NOT_FOUND);
    switch ($namespace) {
       case self::NS_USER:
         $sql = "SELECT usrid AS id, usrmail AS mail, usrpassword AS password, usrstatus AS status FROM users WHERE usrtype='call' AND usrphone=%s";
         $role = Null; //neni treba
         break;
       case self::NS_ADMIN:
         $sql = "SELECT admid AS id, admmail AS mail, admpassword AS password, admstatus AS status FROM admins WHERE admmail=%s";
         $role = 'admin'; //zatim natvrdo
         break;
    }
    $row = dibi::fetch($sql, $username);

    if (!$row) {
      throw new Nette\Security\AuthenticationException("Užívatel s přihlašovacím jméném '$username' nenalezen.", self::IDENTITY_NOT_FOUND);
    }

    if ($row->password !== \Model\BaseModel::calculateHash($password)) {
      throw new Nette\Security\AuthenticationException("Špatné heslo.", self::INVALID_CREDENTIAL);
    }

    unset($row->password);
    return new Nette\Security\Identity($row->id, $role, $row->toArray());
  }
}
