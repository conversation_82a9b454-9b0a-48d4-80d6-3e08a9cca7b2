<?php
namespace Model;
use dibi;
  
class PricelistsModel extends BaseModel {
  
  protected $tableName = "pricelists";
  protected $fieldPrefix = "pli";
  
  /********************* ciselniky *********************/
  /**
  * ciselnik admstatus
  * @return array
  */
  public function getEnumPliStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }
  
  public function getEnumPliId($where="") {
    if ($where != "") $where = " AND ".$where;
    return dibi::fetchPairs("SELECT pliid, pliname FROM ".$this->tableName." WHERE plistatus IN (0,1) ". $where);
  }

}
?>