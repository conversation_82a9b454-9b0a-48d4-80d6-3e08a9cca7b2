<?php
namespace Model;

class UsersModel extends BaseModel {
  
  protected $tableName = "users";
  protected $fieldPrefix = "usr";
  
  public function checkDuplicityEMail($id, $value) {
    $row = \dibi::fetchSingle("SELECT COUNT(*) AS cnt FROM $this->table WHERE usrmail='$value'".($id>0 ? " AND usrid!=$id" : ""));
    if ($row->cnt > 0) throw New Exception("Tento email již existuje.");
  }
  
  public function delete($id) {
    \dibi::query("DELETE FROM numbers WHERE numusrid=%i", $id);
    \dibi::query("DELETE FROM invoices WHERE invusrid=%i", $id);
    return parent::delete($id);
  }
  
  public function insert($data) {
    $id = parent::insert($data);
    //naberu aktualni smlouvu a priradim klientovi
    $conid = (int)\dibi::fetchSingle("SELECT conid FROM contracts WHERE contype='contract' ORDER BY conid DESC");
    if ($conid > 0) {
      $cols = new \Model\ContractsLogModel();
      $cols->insert(array(
        "colusrid"=>$id,
        "colconid"=>$conid,
      ));
    }
    return($id);
  }
  
  /**
  * ciselnik pohlavi
  * 
  */  
  public function getEnumUsrSexId() {
    return array(
      '0' => 'muž',
      '1' => 'žena',
    );
  }
  
  /**
  * ciselnik pohlavi
  * 
  */  
  public function getEnumUsrStatus() {
    return array(
      '0' => 'aktivní účet',
      '1' => 'blokovaný účet',
      '2' => 'dlužník',
    );
  }

  public function getEnumUsrType() {
    return array(
      'call' => 'Volání a Internet',
      'wifi' => 'Internet',
    );
  }
}
?>