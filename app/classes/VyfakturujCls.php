<?php

/**
 * napojení na API vyfakturuj.cz
 */

require_once  LIBS_DIR.'/../vendor/autoload.php';

use Nette\DateTime;


class VyfakturujCls {

  private $vyfakturuj;

  public $ordersToUpdate;

  public function __construct($login, $apiKey) {
    $this->vyfakturuj = new \VyfakturujAPI($login, $apiKey);
  }

  /**
 * @param array $invoice - data o objednávce
 * @return mixed|null|\DibiRow
 */
  public function postInvoice($invoice) {
	  $idFa = NULL;
    //podívám jestli už existuje faktura
    if (!empty($invoice->invvyfid))  {
      $idFa = $invoice->invvyfid;
    }
    if (empty($idFa)) {
      //faktura není, vložím fa
      $response = $this->vyfakturujAddInvoice($invoice);
      //\Tracy\Debugger::log("vyfakturujAddInvoice");
      //\Tracy\Debugger::log($response);
      $idFa = $response["id"];

      $this->ordersToUpdate[$invoice->invid] = [
        'invvyfid' => $idFa,
        'invvyfurl' => $response["url_public_webpage"],
        'invvyfprice' => $response["total"],
        'invvyfcode' => $response["number"],
      ];
    }

    return $idFa;
  }

  public function updateInv($invId, $data) {
    $ret = $this->vyfakturuj->updateInvoice($invId, $data);
    return $ret;
  }


  private function vyfakturujAddInvoice($invoice) {
    $data = $this->vyfakturujPrepareInvoiceData($invoice);
    $response = $this->vyfakturuj->createInvoice($data);
    if (isset($response["status"]) && $response["status"] !== 200) {
      throw new \RuntimeException($response["message"] . " - " . $response["reason"], $response["status"]);
    }

    /*
    //mailuji fakturu
    $data = [
      "type" => 1,
      "to" => $invoice->invmail,
      "pdfAttachment" => false,
      "test" => false
    ];
    $resp = $this->vyfakturuj->invoice_sendMail($response["id"], $data);
    \Tracy\Debugger::log("vyfakturujApi->invoice_sendMail");
    \Tracy\Debugger::log($resp);
    */

    return $response;
  }


    private function vyfakturujPrepareInvoiceData($invoice) {

    $countryCode = 'CZ';
    $idIdentity = 244; //id identity pro telcall.cz

    $d = new DateTime($invoice->invyear . "-" . $invoice->invmonth . "-01");
    $dzpDate = $d->format( 'Y-m-t' );

    $data = array(
      'order_number' => $invoice->invcode,
      'VS' => $invoice->invvarsym,
      'id_number_series' => 226678,
      'id_identity' => $idIdentity,
      'type' => 1,
      'date_taxable_supply' => $dzpDate,
      'calculate_vat' => 1,
      'payment_method' => $this->getVyfakturujPaymetMethodId($invoice->invpaytypid),
      'id_payment_method' => 65784,
      'customer_IC' => $invoice->invic,
      'customer_DIC' => $invoice->invdic,
      'customer_firstname' => $invoice->invrow1,
      //'customer_lastname' => $invoice->invsurname_invoice,
      'customer_street' => $invoice->invrow3,
      'customer_city' => $invoice->invrow4,
      //'customer_zip' => $invoice->invzip_invoice,
      'customer_country_code' => $countryCode,
      'customer_tel' => $invoice->invphone,
      'currency' => "CZK",
      'mail_to' => [
        $invoice->invmail
      ],

      'action_after_create_send_to_eet' => false
    );

    //položky
    $items = array();

    foreach ($invoice->items as $item) {
      $items[] = [
        'text' => $item->initext,
        'unit_price' => $item->inipricecnt,
        'quantity' => 1,
        'vat_rate' => $item->inivat,
        'data' => [
          'year-month' => $item->iniyear . "-" . $item->inimonth,
        ],
      ];
    }

    $data["items"] = $items;

    return $data;
  }

  private function getVyfakturujPaymetMethodId($paymentCode) {
    $array = [
      1 => 1, //Převodem
      2 => 2, //Hotově
      3 => 1, //Inkasem
    ];

    if (isset($array[$paymentCode])) {
      return $array[$paymentCode];
    }
    return 2;
  }

  private function log($text) {
    $date = new \DateTime();
    echo $date->format('H:m:s') . "|" .  $text . "<br>";
  }



  }
