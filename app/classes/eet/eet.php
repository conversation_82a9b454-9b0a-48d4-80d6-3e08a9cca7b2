<?php

/**
 * Created by PhpStorm.
 * User: koblihcz
 * Date: 7.2.2017
 * Time: 9:18
 */

class eet {

  /** @var boolean */
  private $isProduction = false;

  /** @var boolean */
  const LOG_TRANSACTIONS = TRUE;

  const CONNECTION_TIMEZONE = 'Europe/Prague';

  /** @var \<PERSON>denek<PERSON><PERSON><PERSON>\Eet\Connector */
  private $connector = NULL;

  /** @var \<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eet\Config */
  private $eetConfig = NULL;

  /** @var array */
  private $config = NULL;

  /** @var string */
  private $libsPath = '';

  /** @var integer */
  private $logId = '';

  public function __construct($config) {
    $this->libsPath = APP_DIR.'/classes/eet';
    $this->config = $config;

    $this->isProduction = !($this->config["certPassw"] == 'eet');

    require_once  LIBS_DIR.'/../vendor/autoload.php';

    //defaultně nastavím playgroud
    $wsdl = $this->libsPath . '/soap/EETServiceSOAP_Playground.wsdl';
    $certificate = $this->libsPath . '/cert/EET_CA1_Playground-CZ1212121218.p12';

    //pokud v produkci nastavím produkční certifikát
    if ($this->isProduction) {
      $wsdl = $this->libsPath . '/soap/EETServiceSOAP_Production.wsdl';
      $certificate = $this->libsPath . '/cert/1708219528.p12';

    }

    $this->eetConfig = new ZdenekGebauer\Eet\Config($wsdl, $certificate, $this->config["certPassw"]);

    $this->eetConfig->setConnectionTimeout(3) // 3s na připojení k EET
    ->setResponseTimeout(3) // 3s na zpracování requestu
    ->setTraceEnabled(true) // false když nejsou třeba ladící informace ze SOAP clienta
    ->setTimezone(self::CONNECTION_TIMEZONE); // časová zóna, ve které se uvádějí datumy
  }

  private function connect() {
    if (!empty($this->connector)) return true;
    $this->connector = new ZdenekGebauer\Eet\Connector($this->eetConfig);
  }

  public function sendReceipt($ordid, $provozid, $poklid, $price, $crnid=NULL) {
    if (empty($this->config["dic"])) return false;

    //vypočtu základ daně a daň
    $vat =  round($price * 0.1736, 2);
    $priceNoVat = round($price - $vat, 2);

    $logMsg = "";
    $fik = "";
    //zaloguji hodnoty před odesláním
    $dateTime = $this->getNow();
    $this->logStart($ordid, $provozid, $poklid, $price, $dateTime);
    try {
      $receipt = new ZdenekGebauer\Eet\Receipt();
      $this->connect();
      $receipt
        ->setPrvniZaslani(true)
        ->setOvereni(FALSE) // true na playgroundu vyhazuje ServerException::PROCESS_VERIFICATION_ERROR
        ->setDicPoplatnika($this->config["dic"])
        //->setDicPoverujicihoPoplatnika('') // nepovinný údaj
        ->setIdProvozovny($provozid)
        ->setIdPokladny($poklid)
        ->setPoradoveCislo($this->logId)
        ->setDatumTrzby($dateTime)
        ->setCelkovaTrzba($price)
        ->setZakladNepodlehajiciDph(0)
        ->setZakladDan1($priceNoVat)
        ->setDan1($vat)
        ->setZakladDan2(0)
        ->setDan2(0)
        ->setZakladDan3(0)
        ->setDan3(0)
        ->setCestovniSluzba(0)
        ->setPouziteZbozi1(0)
        ->setPouziteZbozi2(0)
        ->setPouziteZbozi3(0)
        ->setUrcenoCerpaniZuctovani(0)
        ->setCerpaniZuctovani(0)
        ->setRezim(ZdenekGebauer\Eet\Receipt::REZIM_TRZBY_BEZNY);

      $fik = $this->connector->send($receipt);
      foreach ($this->connector->getServerWarnings() as $warning) {
        $logMsg .= $warning->getCode().':'.$warning->getMessage()."\n";
      }
    } catch (\Exception $exception) {
      $logMsg .= 'Exception:'.$exception->getCode().'-'.$exception->getMessage()."\n";
    }
    // debug
    if (!empty($this->connector))  {
      $logMsg .= "
  Request duration:" . $this->connector->getLastRequestDuration() . " ms\n
  Request:\n" .
        $this->connector->getLastRequestHeaders() . "\n" .
        $this->connector->getLastRequest() . "\n
  Response:\n" .
        $this->connector->getLastResponseHeaders() . "\n" .
        $this->connector->getLastResponse() . "\n";
    }
    $bkp = $receipt->getBkp();
    $pkp = $receipt->getPkpString();
    //zaloguji konec
    $this->logEnd($fik, $bkp, $pkp, $logMsg);

    if (!empty($fik)) return $fik;
    return false;
  }

  private function getNow() {
    return new \DateTime('now', new \DateTimeZone(self::CONNECTION_TIMEZONE));
  }

  private function logStart($ordid, $provozid, $poklid, $price, $date) {
    $this->logId = 0;
    //zapisu do logu

    $vals = array(
      "loginvid" => $ordid,
      "logprovozid" => $provozid,
      "logpoklid" => $poklid,
      "logprice" => (double)$price,
      "logdatec" => $date,
      );

    $ret = \dibi::insert("eet_log", $vals)->execute(dibi::IDENTIFIER);
    if ($ret === FALSE) {

    } else {
      $this->logId = (int)$ret;
    }
  }

  private function logEnd($fik, $bkp, $pkp, $logMsg) {
    if ($this->logId > 0) {
      $vals = array(
        "logfik" => $fik,
        "logbkp" => $bkp,
        "logpkp" => $pkp,
        "logmsg" => $logMsg,
        "logdateu" =>  new \datetime
      );

      return \dibi::update('eet_log', $vals)
        ->where('logid=%i', $this->logId)
        ->execute();
    } else {
      return false;
    }
  }
}