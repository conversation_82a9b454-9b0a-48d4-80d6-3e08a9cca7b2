<?php
namespace AdminModule;
use dibi;
use Model\InvoicesModel;
use Nette;

final class InvoicePresenter extends BasePresenter {
  /** @persistent */
  public $sPhone = Null;
  /** @persistent */
  public $sName = Null;
  /** @persistent */
  public $sMail = Null;
  /** @persistent */
  public $sIc = Null;
  /** @persistent */
  public $sStatus = Null;

  public $backlink = '';

  public function renderDefault() {
    $this->template->invoicesNotPayed = dibi::fetchAll("
    SELECT *, DATEDIFF(CURDATE(),invpaydate) AS invduedateafter 
    FROM invoices 
    INNER JOIN users ON (usrid=invusrid) 
    WHERE invstatus=0 AND 
    (invcode IS NOT NULL OR (invcode IS NULL AND usrpayrateid=0)) AND
    invtype=%s", $this->sec, " 
    ORDER BY invpaydate
    ");
    
    //nactu obdobi za ktera jsou vygenerovane nejake danove doklady pro tisk
    $this->template->invoicesForPrint = dibi::query("
      SELECT invyear, invmonth, SUM(invpricevat) AS invpricevat 
      FROM invoices 
      WHERE (invdatec + interval 1 year) >= Now() AND 
      invtype=%s", $this->sec, "
      GROUP BY invyear, invmonth");
    
  }

  public function renderOthers() {
    $where = $this->getWhere();
    if (count($where) > 0) $where = array_merge(array(' AND ('), $where, array(')'));
    $query = array("
      SELECT *, DATEDIFF(CURDATE(),invpaydate) AS invduedateafter 
      FROM invoices  
      WHERE invtype='othr'");

    $query = array_merge($query, $where);
    array_push($query, " ORDER BY invpaydate");
    $this->template->rows = dibi::fetchAll($query);
  }

  public function renderEdit($id) {
    $form = $this['editForm'];
    $invoices = new \Model\InvoicesModel();
    $users = new \Model\UsersModel();
    if (!$form->isSubmitted()) {
      if ($id >0) {
        $dataRow = $invoices->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        if (!empty($dataRow->invpaydate)) $dataRow->invpaydate = $this->formatDateFromMySql($dataRow->invpaydate);
        if (!empty($dataRow->invpayeddate)) $dataRow->invpayeddate = $this->formatDateFromMySql($dataRow->invpayeddate);
        $form->setDefaults($dataRow);
        $this->template->dataRow = $dataRow;
        //doplním zákazníka
        $this->template->user = $users->load($dataRow->invusrid);

        if (!empty($dataRow->inveetfik)) {
          $this->template->eet = dibi::fetch("SELECT * FROM eet_log WHERE logfik=%s", $dataRow->inveetfik);
        }
      }
    }
    $this->template->enum_invtype = $invoices->getEnumInvType();
  }
    
  public function actionGetInvoice($id, $dest="I") {
    //vraci template prislusne
    //$template = $this->getTemplate();
    $invoices = New \Model\InvoicesModel();
    $invoice = dibi::fetch("SELECT * FROM invoices WHERE invid=%i", $id);
    $this->getInvoice($invoice, $dest);
  }
  
  public function actionGetInvoices($month, $year) {
    require_once  LIBS_DIR.'/../vendor/autoload.php';
    $mpdf = new \mPDF('utf-8','A4', 12,'',10,10,10,10,9,9,'P');
    $mpdf->useOnlyCoreFonts = true;
    
    $stylesheet = file_get_contents(APP_DIR.'/../templates/invoice.css');
    $mpdf->WriteHTML($stylesheet,1);
    $mpdf->SetDisplayMode('real');
    $mpdf->autoScriptToLang = true;
    $mpdf->autoLangToFont = true;
    
    $invoices = dibi::fetchAll("SELECT * FROM invoices WHERE invcode IS NOT NULL AND invmonth=%i", $month, " AND invyear=%i", $year, " ORDER BY invcode DESC");
    foreach ($invoices as $row) {
      $mpdf = $this->getInvoice($row, 'D', $mpdf);  
    }
    $name = "faktury-$month-$year.pdf";
    $mpdf->Output($name, 'D');  
  }
  
  public function actionPayInvoice($id, $cash=FALSE) {
    $invoices = New \Model\InvoicesModel();
    $invoice = dibi::fetch("SELECT * FROM invoices WHERE invid=%i", $id);
    $user = dibi::fetch("SELECT * FROM users WHERE usrid=%i", $invoice->invusrid);
    if ($cash && $invoice->invpaytypid != 2) {
      $this->flashMessage("Faktura nemá nastavený typ platby hotově! Nelze uhradit.", "err");
      $this->redirect('edit', $id);
    } else if (!$cash && $invoice->invpaytypid == 2) {
      $this->flashMessage("Faktura má nastavený typ platby hotově! Nelze uhradit převodem.", "err");
      $this->redirect('edit', $id);
    } else if ($cash && $invoice->invpaytypid == 2 && $user->usrpayrateid > 0) {
      $this->sendEet($invoice);
    }
    if ($invoices->checkPayed($invoice->invvarsym, $invoice->invpricevat, '', $invoice->invid, $cash=FALSE)) {
      $this->flashMessage("Výzva k platbě var. symbol: $invoice->invvarsym byla uhrazena.");
      $invoice = dibi::fetch("SELECT * FROM invoices WHERE invid=%i", $id);
      $this->mailInvoicePayed('', $invoice->invid);
    } else {
      $this->flashMessage("Výzvu k platbě var. symbol: $invoice->invvarsym se nepodařilo uhradit.", 'err');
    }
    $this->redirect('Admin:default');
  }

  public function actionDeleteInvoice($id) {
    $invoices = New \Model\InvoicesModel();
    if ($invoices->delete($id)) {
      $this->flashMessage("Faktura byla vymazána.");
      $this->redirect('others');
    } else {
      $this->flashMessage("Faktura NEBYLA vymazána.", 'err');
      $this->redirect('edit', $id);
    }

  }

  /********************* facilities *********************/

  protected function createComponentGenInvoicesForm() {
    $invoices = new \Model\InvoicesModel();
    $form = new Nette\Application\UI\Form();
    
    $month = (int)date('m')-1;
    $year = (int)date('Y');
    if ($month == 0) {
      $month = 12;
      $year = $year - 1;
    }
    
    $form->addSelect('month', 'Měsíc', $invoices->getEnumMonId())
      ->setDefaultValue($month)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte měsíc"); 
      
    $form->addText('year', 'Rok', 4)
    ->setDefaultValue($year)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte rok"); 

    $form->addText('nextinvcode', 'Záčít číslovat faktury od čísla', 10);   
    
    $form->addCheckbox('clear', 'Před generováním vymazat cílové období')
    ->setDefaultValue(false); 
    
    $form->addSubmit('save', 'Generovat faktury za '.($this->sec == 'call' ? "volání" : "internet"))->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'genInvoiceFormSubmitted');

    return $form;  
  }
  
  public function genInvoiceFormSubmitted (Nette\Application\UI\Form $form) {
    $plis = new \Model\PricelistsModel();
    
    if ($form['save']->isSubmittedBy()) {
      $vals = $form->getValues();
      $err = false;
      //zjistim jestli negeneruje data do budoucnosti
      $r = dibi::fetch("SELECT YEAR(CURDATE()) AS year, MONTH(CURDATE()) AS month");
      if (($r->year == $vals["year"] && $r->month <= $vals["month"]) || $r->year < $vals["year"]) {
        $err = true;
        $this->flashMessage("Nemůžete generovat fakturu pro aktuální nebo budoucí měsíce.(" . $vals["month"] . "/" . $vals["year"] . ")");
      }
      if ($this->sec == 'call') {
        //kontrola zda ceniky u aktivnich cisel jsou platne
        //nactu pouzite ceniky
        $rows = dibi::fetchAll("SELECT numpliid, numnumber FROM numbers INNER JOIN users ON (usrid=numusrid) WHERE usrtype='call' AND usrstatus IN (0,2) GROUP BY numpliid");
        foreach ($rows as $key => $row) {
          $pli = $plis->load($row->numpliid);
          if ($pli) {
            if ($pli->plistatus == 1) {
              $err = true;
              $this->flashMessage("U čísla " . $row->numnumber . " je pouzitý ceník ID=" . $row->numpliid . " který je blokovaný", 'err');
            }
          } else {
            $err = true;
            $this->flashMessage("Ceník ID=" . $row->numpliid . " neexistuje nebo není u čísla " . $row->numnumber . " vyplněný");
          }
        }
        if ($err) $this->redirect("Invoice:default");
      }

      //vymazu pokud zatrzeno
      if ($vals->clear) {
        dibi::query("DELETE FROM invoices WHERE invtype=%s", $this->sec, " AND invmonth=%i", $vals["month"], " AND invyear=%i", $vals["year"]);
        dibi::query("DELETE FROM invoiceitems WHERE NOT EXISTS (SELECT * FROM invoices WHERE invid=iniinvid)");
      }

      //kontrol zda uz pro dany rok a mesic neni volani
      $itemsCnt = (int)dibi::fetchSingle("SELECT COUNT(*) FROM invoices WHERE invtype=%s", $this->sec, " AND invmonth=%i", $vals["month"], " AND invyear=%i", $vals["year"]);
      if ($itemsCnt > 0) {
        $this->flashMessage("Generování faktur pro tento rok a měsíc už byl proveden!", 'err');
        $this->redirect("Invoice:default");
      }

      //zjistim jestli je kvartal
      $isQuarter = false;
      $isHalfYear = false;
      $isYear = false;
      switch ($vals["month"]) {
        case 3:
          $isQuarter = true;
          $qMonths = "1,2,3";
          break;
        case 6:
          $isQuarter = true;
          $qMonths = "4,5,6";
          $isHalfYear = true;
          $hMonths = "1,2,3,4,5,6";
          break;
        case 9:
          $isQuarter = true;
          $qMonths = "7,8,9";
          break;
        case 12:
          $isQuarter = true;
          $qMonths = "10,11,12";
          $isHalfYear = true;
          $hMonths = "7,8,9,10,11,12";
          $isYear = true;
          $yMonths = "1,2,3,4,5,6,7,8,9,10,11,12";
          break;
      }

      $invoices = new \Model\InvoicesModel();
      $invoiceitems = new \Model\InvoiceitemsModel();
      $numberitems = new \Model\NumberitemsModel();

      //******* VOLANI *********//
      if ($this->sec == 'call') {
        //vezmu vsechny klienty a budu generovat pro kazdeho rovnou fakturu
        $usersNumbers = dibi::query("SELECT usrid, numnumber, numusrid, numpliid, usrstatus FROM users INNER JOIN numbers ON (usrid=numusrid) WHERE usrtype='call' AND usrstatus IN (0,1,2)")->fetchAssoc('usrid,numnumber');

        $nextId = 0;
        //zjistim posledni variabilni symnol faktury - pro nastaveni kodu nasledujicich fa
        $code = (string)dibi::fetchSingle("SELECT invvarsym FROM invoices WHERE invtype=%s", $this->sec, " AND  invyear=%i", $vals["year"], " ORDER BY invvarsym DESC");
        if (empty($code)) {
          //tento rok jeste neni zadna fa
          $nextId = 1;
        } else {
          $nextId = (int)substr($code, -4) + 1;
        }
        $cnt = 0;
        foreach ($usersNumbers as $key => $numbers) {

          //nactu uzivatele
          $userInfo = dibi::fetch("SELECT * FROM users WHERE usrid=%i", $key);
          //zjsitim datum splatnosti
          if ($userInfo === false) {
            $this->flashMessage("Nepodařilo se načíct informace o klientovi ID=$key", 'err');
            return;
          }
          $payDate = dibi::fetchSingle("SELECT CURDATE() + INTERVAL " . $userInfo->usripaydue . " DAY");

          $code = substr($vals["year"], -2) . '9' . substr('000' . $nextId, -4);

          //zjistim zda budu vystavovat fa
          $makeInvoice = false;
          if (($userInfo->usrpayrateid == 1) || ($isQuarter && $userInfo->usrpayrateid == 2) || ($isHalfYear && $userInfo->usrpayrateid == 3) || ($isYear && $userInfo->usrpayrateid == 4)) {
            $makeInvoice = true;
          }

          if ($makeInvoice) {
            if ($cnt == 0 && !empty($vals->nextinvcode)) {
              //kontrola zda zadane cislo neni mensi nez nejblizsi volne cislo
              $invCode = $invoices->getNextInvCode($vals["year"], $this->sec);
              if ((int)$vals->nextinvcode < (int)$invCode) {
                $form->addError("Zadáno neplatné číslo následující faktury. Číslo musí být větší nebo rovno $invCode.");
                return;
              } else {
                $invCode = $vals->nextinvcode;
              }
            } else {
              $invCode = $invoices->getNextInvCode($vals["year"], $this->sec);
            }


          } else {
            $invCode = Null;
          }

          $numbersArr = array();
          foreach ($numbers as $row) {
            $numbersArr[] = $row->numnumber;
          }
          $rows = array();
          if (count($numbersArr) > 0) {
            $numbersStr = "'" . implode("','", $numbersArr) . "'";
            //nactu polozky ze seznamu volani
            $rows = dibi::fetchAll("
              SELECT 
              MAX(nnivat) AS inivat, 
              NULL AS ininumber,
              ROUND(SUM(nnicnt), 2) AS inicnt, 
              ROUND(SUM(nnicntfree), 2) AS inicntfree, 
              ROUND(SUM(nnipricecnt), 2) AS inipricecnt, 
              nnitext AS initext, 
              nniunit AS iniunit, 
              Now() AS inidatec, 
              NULL AS inidateu 
              FROM numberitems 
              WHERE 
              nnimonth=%i", $vals["month"], " AND 
              nniyear=%i", $vals["year"], " AND
              nninumber IN (" . $numbersStr . ")
              GROUP BY nnitext 
              ORDER BY nnitext
              ");

            //zjistim volne minuty a SMS
            $freeSMS = 0;
            $freeSecs = 0;
            foreach ($rows as $i => $row) {
              if ($row->initext === 'Volné minuty') {
                $freeSecs = $row->inicnt;
                unset($rows[$i]);
              }
              if ($row->initext === 'Volné SMS') {
                $freeSMS = $row->inicnt;
                unset($rows[$i]);
              }
            }
            //aktualizuji volné minuty a SMS u příslušné položky
            foreach ($rows as $row) {
              if ($row->initext === 'Volání mobilní a pevné síte ČR') {
                $row->inicntfree = $freeSecs;
              }
              if ($row->initext === 'SMS národní sítě') {
                $row->inicntfree = $freeSMS;
              }
            }
          }

          if (count($rows) > 0 && count($numbersArr) > 0) {

            if ($userInfo->usrstatus == 1) {
              $this->flashMessage("Klient " . $userInfo->usrname . ", ID=" . $userInfo->usrid . " má zablokovaný účet. Nevygeneruje se mu faktura.", 'err');
              continue;
            }

            //vytvorim fa
            $iVals = array(
              'invvarsym' => $code,
              'invcode' => $invCode,
              'invtype' => 'call',
              'invusrid' => (int)$key,
              'invpaytypid' => (int)$userInfo->usrpaytypid,
              'invmonth' => (int)$vals["month"],
              'invyear' => (int)$vals["year"],
              'invrow1' => $userInfo->usrirow1,
              'invrow2' => $userInfo->usrirow2,
              'invrow3' => $userInfo->usrirow3,
              'invrow4' => $userInfo->usrirow4,
              'invic' => $userInfo->usriic,
              'invdic' => $userInfo->usridic,
              'invphone' => $userInfo->usrphone,
              'invmail' => $userInfo->usrmail,
              'invpaydate' => $payDate,
              'invstatus' => 0,

            );
            $id = (int)$invoices->insert($iVals);
            //vlozim polozky
            if ($id > 0) {
              $nextId++;
              $cnt++;
              foreach ($rows as $rowi) {
                $rowi->iniinvid = $id;
                $rowi->inimonth = $iVals["invmonth"];
                $rowi->iniyear = $iVals["invyear"];
                $invoiceitems->insert($rowi);
              }
              $where = "";
              $period = "";
              if ($userInfo->usrpayrateid > 0) {
                //pokud je konec fakturacniho obdobi vlozim polozky z predeslych vyzev - beru jen vyzvy ne faktury
                if ($isQuarter && $userInfo->usrpayrateid == 2) {
                  $where = " invmonth IN ($qMonths) AND invyear=" . $vals["year"];
                } else if ($isHalfYear && $userInfo->usrpayrateid == 3) {
                  $where = " invmonth IN ($hMonths) AND invyear=" . $vals["year"];
                } else if ($isYear && $userInfo->usrpayrateid == 4) {
                  $where = " invyear=" . $vals["year"];
                }
                if ($where != "") {
                  $addRows = dibi::fetchAll("
                    SELECT invoiceitems.*, invoices.invmonth, invoices.invyear 
                    FROM invoiceitems 
                    INNER JOIN invoices ON (invid=iniinvid)
                    WHERE invcode IS NULL AND invtype='call' AND invusrid=" . $userInfo->usrid . " AND $where  
                  ");
                  //doplnim polozky z predchozich vyzev
                  foreach ($addRows as $arow) {
                    $arow->iniinvid = $id;
                    $arow->inimonth = $arow->invmonth;
                    $arow->iniyear = $arow->invyear;
                    unset($arow->invmonth);
                    unset($arow->invyear);
                    unset($arow->iniid);
                    $invoiceitems->insert($arow);
                  }
                  //navazu vyzvy k teto fakture
                  dibi::query("
                  UPDATE invoices SET invinvid=%i", $id, " 
                  WHERE invcode IS NULL AND invtype='call' AND 
                  invusrid=" . $userInfo->usrid . " AND 
                  $where ");
                }
                if ($isQuarter) $period = "$qMonths/" . $vals["year"];
              }

              //pokud uživatel má vyplněnou cenu za internet vložím do fa
              if ((int)$userInfo->usrinetprice > 0) {
                $iVals = array(
                  'iniinvid'=> $id,
                  'inivat'=> $this->config["INVOICE_DPHLEVEL_0"],
                  'inicnt'=> 1,
                  'iniprice' => (int)$userInfo->usrinetprice,
                  'inipricecnt' => (int)$userInfo->usrinetprice,
                  'initext' => 'Připojení na internet za měsíc '.$vals["month"]."/".$vals["year"],
                );

                $invoiceitems->insert($iVals);
              }

              //pokud uživatel má slevu vložím do fa
              if (abs((double)$userInfo->usrdiscount) > 0) {
                $iVals = array(
                  'iniinvid' => $id,
                  'inivat' => $this->config["INVOICE_DPHLEVEL_0"],
                  'inicnt' => 1,
                  'inimonth' => $iVals["invmonth"],
                  'iniyear' => $iVals["invyear"],
                  //'iniprice' => round(abs((float)$userInfo->usrdiscount) * -1),
                  'inipricecnt' => round(abs((float)$userInfo->usrdiscount) * -1),
                  'initext' => 'Sleva na zákazníka',
                );

                $invoiceitems->insert($iVals);
                /*if ($userInfo->usrid === 478) {
                    print_r($iVals);
                    die();
                }*/
              }

              //aktualizuju cenu v hlavicce fa
              $iniRows = dibi::fetch("SELECT ROUND(SUM(inipricecnt), 2) AS iniprice, SUM(inipricecnt+ROUND((inipricecnt*inivat/100),2)) AS inipricevat FROM invoiceitems WHERE iniinvid=%i", $id);
              //aktualizuju fa
              $iVals = array(
                'invprice' => $iniRows->iniprice,
                'invpricevat' => round($iniRows->inipricevat, 0),
              );
              if (!empty($period)) $iVals["invperiod"] = $period;
              $invoices->update($id, $iVals);
            }
          }
        }
        $this->flashMessage("Vytvořeno $cnt faktur za volání");
      } else if ($this->sec == 'wifi') {
        //******* INTERNET *********//
        //vezmu vsechny klienty a budu generovat pro kazdeho vyzvu k platbe - za internet
        $users = dibi::query("SELECT * FROM users WHERE usrtype='wifi' AND usrstatus IN (0,2) AND usrpricewifi > 0");
        $cnt = 0;
        foreach ($users as $user) {
          //zjistim posledni variabilni symnol faktury - pro nastaveni kodu nasledujicich fa
          $code = $invoices->getNextVarSym($vals["year"]);
          $payDate = dibi::fetchSingle("SELECT LAST_DAY(Now())");
          //vytvorim fa
          //zjistim zda budu vystavovat fa
          $makeInvoice = true;
          if ($user->usrpayrateid == 0) $makeInvoice = false;
          if ($makeInvoice) {
            if ($cnt == 0 && !empty($vals->nextinvcode)) {
              //kontrola zda zadane cislo neni mensi nez nejblizsi volne cislo
              $invCode = $invoices->getNextInvCode($vals["year"], $this->sec);
              if ((int)$vals->nextinvcode < (int)$invCode) {
                $form->addError("Zadáno neplatné číslo následující faktury. Číslo musí být větší nebo rovno $invCode.");
                return;
              } else {
                $invCode = $vals->nextinvcode;
              }
            } else {
              $invCode = $invoices->getNextInvCode($vals["year"], $this->sec);
            }
          } else {
            $invCode = Null;
          }

          $iVals = array(
            'invvarsym'=> $code,
            'invcode'=> $invCode,
            'invtype'=> 'wifi',
            'invusrid'=> $user->usrid,
            'invpaytypid'=> (int)$user->usrpaytypid,
            'invmonth'=> (int)$vals["month"],
            'invyear' => (int)$vals["year"],
            'invrow1' => $user->usrirow1,
            'invrow2' => $user->usrirow2,
            'invrow3' => $user->usrirow3,
            'invrow4' => $user->usrirow4,
            'invic' => $user->usriic,
            'invdic' => $user->usridic,
            'invphone' => $user->usrphone,
            'invmail' => $user->usrmail,
            'invpaydate' => $payDate,
            'invstatus' => 0,
          );
          $id = (int)$invoices->insert($iVals);
          //vlozim polozku
          if ($id > 0) {
            //vlozim polozku za pausal
            $iVals = array(
              'iniinvid'=> $id,
              'inivat'=> $this->config["INVOICE_DPHLEVEL_0"],
              'inicnt'=> 1,
              'iniprice' => $user->usrpricewifi,
              'inipricecnt' => $user->usrpricewifi,
              'initext' => 'Wifi připojení na internet za měsíc '.$vals["month"]."/".$vals["year"],
            );
            $invoiceitems->insert($iVals);
          }
          $cnt++;
          //aktualizuju cenu v hlavicce fa
          $iniRows = dibi::fetch("SELECT ROUND(SUM(inipricecnt), 2) AS iniprice, SUM(inipricecnt+ROUND((inipricecnt*inivat/100),2)) AS inipricevat FROM invoiceitems WHERE iniinvid=%i", $id);
          //aktualizuju fa
          $iVals = array(
            'invprice' => round($iniRows->iniprice, 2),
            'invpricevat' => round($iniRows->inipricevat, 0),
          );
          $invoices->update($id, $iVals);
        }
        $this->flashMessage("Vytvořeno $cnt faktur za internet");

      }
    }

    //projdu vsechny vygenerovane faktury a vygeneruju eet
    $rows = dibi::fetchAll("SELECT * FROM invoices WHERE invpaytypid=2 AND invcode IS NOT NULL AND invtype=%s", $this->sec, " AND invmonth=%i", $vals["month"], " AND invyear=%i", $vals["year"]);
    foreach ($rows as $row) {
      //$this->sendEet($row, FALSE);
    }

    //vymazu stare data za volani
    $year = $vals["year"]; 
    $month = $vals["month"] - 3;
    if ($vals["month"] <= 3) {
      $year = $vals["year"]-1; 
      $month = 12 + ($vals["month"] - 3);
    }
    dibi::query("DELETE FROM calls WHERE (calmonth<%i", $month," AND calyear=%i", $year, ") OR calyear<%i", $year);
    
    //vymazu stare faktury
    $year = $vals["year"] - 3; 
    dibi::query("DELETE FROM invoices WHERE invyear<%i", $year, " OR (invyear=%i", $year, " AND invmonth<=", $vals["month"], ")");
    dibi::query("DELETE FROM numberitems WHERE nniyear<%i", $year, " OR (nniyear=%i", $year, " AND nnimonth<=", $vals["month"], ")");
    dibi::query("DELETE FROM invoiceitems WHERE NOT EXISTS (SELECT * FROM invoices WHERE iniinvid=invid)");
    
    $this->redirect('this');
  }

  protected function createComponentMailInvoicesForm() {
    $invoices = new \Model\InvoicesModel();
    $form = new Nette\Application\UI\Form();
    
    $month = (int)date('m')-1;
    $year = (int)date('Y');
    if ($month == 0) {
      $month = 12;
      $year = $year - 1;
    }
    
    $form->addSelect('month', 'Měsíc', $invoices->getEnumMonId())
      ->setDefaultValue($month)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte měsíc"); 
      
    $form->addText('year', 'Rok', 4)
    ->setDefaultValue($year)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte rok"); 
    
    $form->addSubmit('save', 'mailovat výzvy k platbě za '.($this->sec == 'call' ? "volání" : "internet"))->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'mailInvoicesFormSubmitted');

    return $form;  
  }
  
  public function mailInvoicesFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $vals = $form->getValues();
      //vsechyn neuhrazene fa za dane obdobi
      $invs = new \Model\InvoicesModel();
      $rows = dibi::fetchAll("
        SELECT * FROM invoices 
        INNER JOIN users ON (invusrid=usrid) 
        WHERE 
          invstatus=0 AND
          invtype='" . $this->sec . "' AND    
          invmonth=" . (int)$vals["month"]  . " AND 
          invyear=" . (int)$vals["year"]  . " AND
          (invcode IS NOT NULL OR (invcode IS NULL AND usrpayrateid=0)) AND 
          usrstatus IN (0,2)
      ");

      foreach ($rows as $key => $row) {
        $template = $this->getTemplate();
        $rows[$key]->invpricevat = $invs->getPayValue($row);    
        $template->invoice = $row;
        if ($row->usrsendtypid == 1) {
          $template->setFile(WWW_DIR . '/../templates/mailVyzvaPlatba.phtml');
          $this->sendMail($row->usrmail, "Vyúčtování služeb", $template);
        } else if ($row->usrsendtypid == 2) {
          if (!empty($row->invcode) || $row->usrpayrateid == 0) {
            $template->setFile(WWW_DIR . '/../templates/smsVyzvaPlatba.phtml');
            $this->sendSms($row->usrphone, $template);
            sleep(5);
          }
        }
      }

    }
    $this->flashMessage("Výzvy k platbě byly odelány.");
    $this->redirect("default");
  }


  /**
   * @return Nette\Application\UI\Form
   */
  protected function createComponentVyfakturujInvoicesForm() {
    $form = new Nette\Application\UI\Form();
    $form->addSubmit('save', 'generovat faktury do vyfakturuj za '.($this->sec == 'call' ? "volání" : "internet"))->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'vyfakturujInvoicesFormSubmitted');
    return $form;
  }

  /**
   * @param Nette\Application\UI\Form $form
   * @throws Nette\Application\AbortException
   */
  public function vyfakturujInvoicesFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVals = $form->getValues();
      $this->vyfakturujSend();
    }
    $this->flashMessage("Faktury byly vygenerovány.");
    $this->redirect("default");
  }
  
  protected function createComponentGenInkasoForm() {
    $invoices = new \Model\InvoicesModel();
    $form = new Nette\Application\UI\Form();
    
    $month = (int)date('m')-1;
    $year = (int)date('Y');
    if ($month == 0) {
      $month = 12;
      $year = $year - 1;
    }
    
    $form->addSelect('month', 'Měsíc', $invoices->getEnumMonId())
      ->setDefaultValue($month)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte měsíc"); 
      
    $form->addText('year', 'Rok', 4)
    ->setDefaultValue($year)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte rok"); 
    
    $form->addSubmit('save1', 'Platby na fakturu za '.($this->sec == 'call' ? "volání" : "internet"))->getControlPrototype()->class('default');
    $form->addSubmit('save2', 'Platby bez faktury za '.($this->sec == 'call' ? "volání" : "internet"))->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'genInkasoFormSubmitted');

    return $form;  
  }
  
  public function genInkasoFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save1']->isSubmittedBy() || $form['save2']->isSubmittedBy()) {
      $vals = $form->getValues();
      //vsechny fa za dane obdobi co co jsou platba inkasem
      $invs = new \Model\InvoicesModel();
      $rows = dibi::fetchAll("
        SELECT *, CURDATE() + INTERVAL 1 DAY as curdate FROM invoices 
        INNER JOIN users ON (invusrid=usrid) 
        WHERE 
          invpaytypid=3 AND 
          (invcode IS NOT NULL OR (invcode IS NULL AND usrpayrateid=0)) AND 
          invmonth=%i", $vals->month, " AND 
          invyear=%i", $vals->year, " AND  
          usrstatus IN (0,2) AND 
          invtype=%s", $this->sec, " AND 
          usraccnumber IS NOT NULL AND 
          usracccode IS NOT NULL
      ");
      $params = $this->neonParameters["fio"];
      $params2 = $this->neonParameters["fio2"];
      $fio = new \FioApi($params);
      $fio2 = new \FioApi($params2);
      foreach ($rows as $key => $row) {
        $item = array(
          'amount' => (double)$row->invpricevat,
          'accountTo' => $row->usraccnumber,
          'bankCode' => $row->usracccode,
          'ks' => '',
          'vs' => $row->invvarsym,
          'ss' => $row->invusrid,
          'date' => \Nette\DateTime::from($row->curdate)->format('Y-m-d'),
          'messageForRecipient' => 'Telcall za sluzby '.$row->invmonth."/".$row->invyear,
          'comment' => $row->invrow1,
          'paymentReason' => '',
          'paymentType' => \FioApi::PAYMENT_INKASO,
        );
        if (!empty($row->invcode)) {
            $fio->addPayment($item);
        } else {
            $fio2->addPayment($item);
        }
      }

      if ($form['save1']->isSubmittedBy()) {
          if ($fio->saveXml() === FALSE) {
            $this->flashMessage("Vygenerovaný soubor pro FIO fakturační je prázdný");
            $this->redirect("default");
          }
      } else if ($form['save2']->isSubmittedBy()) {
          if ($fio2->saveXml() === FALSE) {
              $this->flashMessage("Vygenerovaný soubor pro FIO nefakturační je prázdný");
              $this->redirect("default");
          }
      }

      $this->terminate();
    }
  }

  protected function createComponentGenEetForm() {
    $invoices = new \Model\InvoicesModel();
    $form = new Nette\Application\UI\Form();

    $month = (int)date('m')-1;
    $year = (int)date('Y');
    if ($month == 0) {
      $month = 12;
      $year = $year - 1;
    }

    $form->addSelect('month', 'Měsíc', $invoices->getEnumMonId())
      ->setDefaultValue($month)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte měsíc");

    $form->addText('year', 'Rok', 4)
    ->setDefaultValue($year)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte rok");

    $form->addSubmit('save', 'generovat inkaso za '.($this->sec == 'call' ? "volání" : "internet"))->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'genEetFormSubmitted');

    return $form;
  }

  public function genEetFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $vals = $form->getValues();
      //vsechny fa za dane obdobi co co jsou platba hotově
      $invs = new \Model\InvoicesModel();
      $rows = dibi::fetchAll("
        SELECT * 
        FROM invoices 
        INNER JOIN users ON (invusrid=usrid) 
        WHERE 
          invpaytypid=2 AND 
          (invcode IS NOT NULL OR (invcode IS NULL AND usrpayrateid=0)) AND 
          invmonth=%i", $vals->month, " AND 
          invyear=%i", $vals->year, " AND  
          usrstatus IN (0,2) AND 
          invtype=%s", $this->sec, " AND 
          inveetfik is null 
      ");

      foreach ($rows as $key => $row) {
        $this->sendEet($row);
      }
      $this->redirect("default");
    }
  }

  
  protected function createComponentPaymentsImportForm() {
    $invoices = new \Model\InvoicesModel();
    $form = new Nette\Application\UI\Form();
    
    $form->addUpload('imp_file', "Importní soubor", 100)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte importní soubor");
    
    $form->addSubmit('save', 'Párovat platby')->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'paymentImportFormSubmitted');

    return $form;  
  }
  
  public function paymentImportFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $vals = $form->getValues();
      if (!$vals["imp_file"][0]->isOk()) {
        $form->addError("Neplatný importní soubor.");
        return;
      } else {
        $fileName = $vals["imp_file"][0]->getTemporaryFile();
      }
      
      $csv = new \parseCSV();
      $csv->encoding('cp1250', 'UTF-8');
      $csv->delimiter = ";";
      $csv->offset = 10;
      $csv->heading = false;
      $csv->parse($fileName);
      
      $cnt = 0;
      $cntinsER = 0;
      //$calls = new \Model\CallsModel();
      $arr = array();
      $invoices = new \Model\InvoicesModel();
      $cntPayed = 0;
      foreach ($csv->data as $key => $row) {
        $cnt++;
        if (!empty($row[6])) {
          $varSym = (string)$row[6];
          $price = (double)$this->formatNumber($row[2]);
          //nactu zda neexistuje takova neuhrazena fa
          if (!empty($varSym) && $price > 0) {
            $date = $this->formatDate($row[1]);
            if ($invoices->checkPayed($varSym, $price, $date)) {
              $cntPayed ++;
              $this->mailInvoicePayed($varSym, $invid=0);
            }  
          }
        }
      }
      $this->flashMessage("Úspěšně uhrazeno $cntPayed faktur");
      $this->redirect('default');  
    }
  }
  
  protected function mailInvoicePayed($varSym, $invid=0) {
    if ($invid > 0) {
      $invoice = dibi::fetch("SELECT * FROM invoices LEFT JOIN users ON (usrid=invusrid) WHERE invid=%i", $invid);
    } else {
      $invoice = dibi::fetch("SELECT * FROM invoices LEFT JOIN users ON (usrid=invusrid) WHERE invvarsym=%s", $varSym);
    }
    $template = $this->getTemplate();
    $template->setFile(Nette\Environment::expand('%wwwDir%/../templates/mailZaplaceno.phtml'));
    $template->invoice = $invoice;
    $mail = (!empty($invoice->usrmail) ? $invoice->usrmail : $invoice->invmail);
    $this->sendMail($mail, "Úhrada přijata", $template);
  }
  
  
  protected function createComponentEditForm() {
    $invoices = new \Model\InvoicesModel();
    $form = new Nette\Application\UI\Form();
    $id = $this->getParam("id");
    
    $inv = $invoices->load($id);

    $form->addSelect('invpaytypid', 'Typ platby', $invoices->getEnumPayTypId())
      ->setPrompt("Vyberte ...")
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");
      
    $form->addText('invrow1', 'Název firmy', 60)
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");
      
    $form->addText('invrow2', 'Jméno', 60);
    $form->addText('invrow3', 'Ulice', 60);
    $form->addText('invrow4', 'Město + PSČ', 60);
    
    $form->addText('invic', 'IČ', 40);
    $form->addText('invdic', 'DIČ', 40);
    
    $form->addText('invphone', 'Telefon', 40);
    $form->addText('invmail', 'Email', 40);
    
    $form->addText('invpaydate', 'Datum splatnosti', 40);
    $form->addText('invpayeddate', 'Datum úhrady', 40);

    $form->addCheckbox("ordnodph", "Faktura bez DPH - členský stát EU");
    $form->addCheckbox("ordnodphrc", "Faktura bez DPH - Reverse charge");

    $form->addSelect('invbankacc', 'Bankovní účet:', $this->getBankAccounts())
      ->setPrompt("Vyberte ...");
    
    $form->addSelect('invstatus', 'Status', $invoices->getEnumInvStatus())
      ->setPrompt("Vyberte ...")
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno."); 
    
    $form->addSubmit('save', 'Uložit hlavičku')->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    return $form;  
  }
  
  public function editFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $vals = $form->getValues();
      if (!empty($vals->invpaydate)) {
        $vals->invpaydate = $this->formatDate($vals->invpaydate);
      } else {
        $vals->invpayeddate == NULL;
      }
      $invpayeddate = trim($vals->invpayeddate);
      if (!empty($invpayeddate)) {
        $vals->invpayeddate = $this->formatDate($vals->invpayeddate);
      } else {
        $vals->invpayeddate = NULL;
      }
      
      $id = (int)$this->getParam('id');
      $invs = new \Model\InvoicesModel();
      try {
        $invs->update($id, $vals);
        $this->flashMessage("Změny byly uloženy.");
      } catch (Exception $e) {
        $form->addError($e->getMessage());
      }
      
      $this->redirect('edit', $id);  
    }
  }
  
  protected function createComponentCallItemsEditForm() {
    $invoices = new \Model\InvoicesModel();
    $form = new Nette\Application\UI\Form();
    $id = $this->getParam("id");
    $inv = $invoices->load($id);
    
    //nactu cisla klienta
    $rows = dibi::fetchAll("
      SELECT numpliid, numnumber 
      FROM numbers 
      INNER JOIN users ON (usrid=numusrid) 
      WHERE usrid=%i", $inv->invusrid, " AND  
      (numdatefrom IS NULL OR (numdatefrom <= DATE('".$inv->invyear."-".$inv->invmonth."-1'))) AND 
      (numdateto IS NULL OR (numdateto >= DATE('".$inv->invyear."-".$inv->invmonth."-1')))  
      GROUP BY numnumber");
    
    $numsArray = array();
    foreach ($rows as $row) {
      $numsArray[$row->numnumber] = $row->numnumber;  
    }
    $numbers = implode("','", $numsArray);
    $items = dibi::fetchAll("SELECT * FROM numberitems WHERE nnimonth=%i", $inv->invmonth, " AND nniyear=%i", $inv->invyear, " AND nninumber IN ('".$numbers."')");
    
    $cont = $form->addContainer('items');
    
    $cont = $form["items"]->addContainer(0);
    $cont->addHidden("nniyear", $inv->invyear);
    $cont->addHidden("nnimonth", $inv->invmonth);
    
    $cont->addSelect("nninumber", "Telefon", $numsArray)
      ->setPrompt("Vyberte ...");
    
    $vat = array(
      $this->config["INVOICE_DPHLEVEL_0"] => $this->config["INVOICE_DPHLEVEL_0"].'%',
      $this->config["INVOICE_DPHLEVEL_1"] => $this->config["INVOICE_DPHLEVEL_1"].'%',
      0 => '0%',
    );
    
    $cont->addSelect("nnivat", 'Sazba', $vat)
      ->addConditionOn($form["items"][0]["nninumber"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");
    
    $unit = array(
      '' => '',
      's' => 's',
      'ks' => 'ks',
      'kB' => 'kB',
      'min' => 'min',
    );
    $cont->addSelect("nniunit", 'Jednotka', $unit)
      ->setPrompt("");
    
    $cont->addText('nniprice', 'Cena', 8)
      ->addConditionOn($form["items"][0]["nninumber"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");  
    
    $cont->addText('nnicnt', 'Množství', 8)
      ->addConditionOn($form["items"][0]["nninumber"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");
    
    $cont->addText('nnicntfree', 'Volné množství', 8);
    
    $cont->addText('nnipricecnt', 'Cena', 8)
      ->addConditionOn($form["items"][0]["nninumber"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");
    
    $cont->addText('nnitext', 'Text', 60)
      ->addConditionOn($form["items"][0]["nninumber"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");
    
    foreach ($items as $key => $row) {
      $cont = $form["items"]->addContainer($row->nniid);
      $cont->addHidden("nniyear", $inv->invyear);
      $cont->addHidden("nnimonth", $inv->invmonth);

      $cont->addSelect("nninumber", "Telefon", $numsArray)
        ->setDefaultValue($row->nninumber)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

      $cont->addSelect("nnivat", 'Sazba', $vat)
        ->setDefaultValue($row->nnivat)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

      $cont->addSelect("nniunit", 'Jednotka', $unit)
        ->setDefaultValue($row->nniunit)
        ->setPrompt("");

      $cont->addText('nniprice', 'Cena', 8)
        ->setDefaultValue($row->nniprice)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

      $cont->addText('nnicnt', 'Množství', 8)
        ->setDefaultValue($row->nnicnt)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

      $cont->addText('nnicntfree', 'Volné množství', 8)
        ->setDefaultValue($row->nnicntfree);

      $cont->addText('nnipricecnt', 'Cena', 8)
        ->setDefaultValue($row->nnipricecnt)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");
        
      $cont->addText('nnitext', 'Text', 60)
        ->setDefaultValue($row->nnitext)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");
        
      $cont->addCheckbox('delete', 'vymazat');  
    }
        
    $form->addSubmit('save', 'Uložit položky')->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'callItemsEditFormSubmitted');

    return $form;  
  }

  protected function createComponentItemsEditForm() {
    $invoices = new \Model\InvoicesModel();
    $form = new Nette\Application\UI\Form();
    $id = $this->getParam("id");
    $inv = $invoices->load($id);

    $cont = $form->addContainer('items');

    $vat = array(
      $this->config["INVOICE_DPHLEVEL_0"] => $this->config["INVOICE_DPHLEVEL_0"].'%',
      $this->config["INVOICE_DPHLEVEL_1"] => $this->config["INVOICE_DPHLEVEL_1"].'%',
      0 => '0%',
    );

    $items = dibi::fetchAll("SELECT * FROM invoiceitems WHERE iniinvid=%i", $inv->invid);
    foreach ($items as $key => $row) {
      $cont = $form["items"]->addContainer($row->iniid);

      $cont->addText('iniprice', 'Cena', 8)
        ->setDefaultValue($row->iniprice)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

      $cont->addText('inicnt', 'Počet', 2)
        ->setDefaultValue($row->inicnt)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

      $cont->addSelect("inivat", 'Sazba', $vat)
        ->setDefaultValue($row->inivat)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

      $cont->addText('initext', 'Text', 60)
        ->setDefaultValue($row->initext)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

      $cont->addCheckbox('delete', 'vymazat');
    }

    $cont = $form["items"]->addContainer(0);
    $cont->addText('iniprice', 'Cena', 8);

    $cont->addText('inicnt', 'Počet', 2)
      ->addConditionOn($form["items"][0]["iniprice"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

    $cont->addSelect("inivat", 'Sazba', $vat)
      ->addConditionOn($form["items"][0]["iniprice"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

    $cont->addText('initext', 'Text', 60)
      ->addConditionOn($form["items"][0]["iniprice"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

    $form->addSubmit('save', 'Uložit položky')->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'itemsEditFormSubmitted');

    return $form;
  }

  public function callItemsEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $vals = $form->getValues();
      $nnis = new \Model\NumberitemsModel();
      $usrs = new \Model\UsersModel();
      $invs = new \Model\InvoicesModel();
      $invoiceitems = new \Model\InvoiceitemsModel();
      $id = $this->getParameter("id");
      $inv = $invs->load($id);
      $usr = $usrs->load($inv->invusrid);
      try {
        foreach ($vals["items"] as $key => $row) {
          if ($key == 0) {
            if (!empty($row->nninumber)) {
              //naformatuju cisla
              $row->nniprice = $this->formatNumber($row->nniprice);
              $row->nnipricecnt = $this->formatNumber($row->nnipricecnt);
              $nnids[] = $nnis->insert($row);  
            }  
          } else {
            if ($row->delete) {
              $nnis->delete($key);  
            } else {
              unset($row->delete);
              $row->nniprice = $this->formatNumber($row->nniprice);
              $row->nnipricecnt = $this->formatNumber($row->nnipricecnt);
              $nnis->update($key, $row);  
              $nnids[] = $key;
            }
          }  
        }
        
        //vymazu polozky fa
        dibi::query("DELETE FROM invoiceitems WHERE iniinvid=%i", $id);
        
        //prepocitam polozky fa
        $rows = dibi::fetchAll("
            SELECT 
            MAX(nnivat) AS inivat, 
            NULL AS ininumber,
            ROUND(SUM(nnicnt), 2) AS inicnt, 
            ROUND(SUM(nnicntfree), 2) AS inicntfree, 
            ROUND(SUM(nnipricecnt), 2) AS inipricecnt, 
            nnitext AS initext, 
            nniunit AS iniunit, 
            Now() AS inidatec, 
            NULL AS inidateu 
            FROM numberitems 
            WHERE 
            nniid IN (".implode(',', $nnids).")
            GROUP BY nnitext 
            ORDER BY nnitext
            ");
        foreach ($rows as $rowi) {
          $rowi->iniinvid = $id;
          $rowi->inimonth = $inv->invmonth;
          $rowi->iniyear = $inv->invyear;
          $invoiceitems->insert($rowi);
        }
        
        $where = "";
        $period = "";
        if ($usr->usrpayrateid > 0) {
          //zjistim jestli je kvartal
          $isQuarter = false;
          $isHalfYear = false;
          $isYear = false;
          switch ($inv->invmonth) {
            case 3:
              $isQuarter = true;
              $qMonths = "1,2,3";
              break;
            case 6:
              $isQuarter = true;
              $qMonths = "4,5,6";
              $isHalfYear = true;
              $hMonths = "1,2,3,4,5,6";
              break;
            case 9:
              $isQuarter = true;
              $qMonths = "7,8,9";
              break;
            case 12:
              $isQuarter = true;
              $qMonths = "10,11,12";
              $isHalfYear = true;
              $hMonths = "7,8,9,10,11,12";
              $isYear = true;
              $yMonths = "1,2,3,4,5,6,7,8,9,10,11,12";
              break;  
          }
          
          //pokud je konec fakturacniho obdobi vlozim polozky z predeslych vyzev - beru jen vyzvy ne faktury
          if ($isQuarter && $usr->usrpayrateid == 2) { 
            $where = " invmonth IN ($qMonths) AND invyear=".$inv->invyear;  
          } else if ($isHalfYear && $usr->usrpayrateid == 3) {
            $where = " invmonth IN ($hMonths) AND invyear=".$inv->invyear;
          } else if ($isYear && $usr->usrpayrateid == 4) {
            $where = " invyear=".$inv->invyear;
          }
          if ($where != "") {
            $addRows = dibi::fetchAll("
              SELECT invoiceitems.*, invoices.invmonth, invoices.invyear 
              FROM invoiceitems 
              INNER JOIN invoices ON (invid=iniinvid)
              WHERE invcode IS NULL AND invtype='call' AND invusrid=".$usr->usrid." AND $where  
            ");
            //doplnim polozky z predchozich vyzev
            foreach ($addRows as $arow) {
              $arow->iniinvid = $id;
              $arow->inimonth = $arow->invmonth;
              $arow->iniyear = $arow->invyear;
              unset($arow->invmonth);
              unset($arow->invyear);
              unset($arow->iniid);
              $invoiceitems->insert($arow);
            }
          }
          if ($isQuarter) $period = "$qMonths/".$inv->invyear;  
        }
        //aktualizuju cenu v hlavicce fa
        $iniRows = dibi::fetch("SELECT ROUND(SUM(inipricecnt), 2) AS iniprice, SUM(inipricecnt+ROUND((inipricecnt*inivat/100),2)) AS inipricevat FROM invoiceitems WHERE iniinvid=%i", $id);
        //aktualizuju fa
        $iVals = array(
          'invprice' => $iniRows->iniprice,
          'invpricevat' => round($iniRows->inipricevat, 0),
        );
        if (!empty($period)) $iVals["invperiod"] = $period;
        $invs->update($id, $iVals);
      } catch (Exception $e) {
        $form->addError($e->getMessage());
      }
      $this->redirect('edit', $id);  
    }
  }

  public function itemsEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $vals = $form->getValues();
      $usrs = new \Model\UsersModel();
      $invs = new \Model\InvoicesModel();
      $invoiceitems = new \Model\InvoiceitemsModel();
      $id = $this->getParameter("id");
      $inv = $invs->load($id);
      $usr = $usrs->load($inv->invusrid);
      try {
        foreach ($vals["items"] as $key => $row) {
          $iniVals = $row;
          if ($key > 0) {
            if ($iniVals["delete"]) {
              $invoiceitems->delete($key);
            } else {
              unset($iniVals["delete"]);
              $iniVals["inipricecnt"] = $iniVals["iniprice"]*$iniVals["inicnt"];
              $invoiceitems->update($key, $iniVals);
            }
          } else if ($key == 0 && $iniVals["iniprice"]) {
            $iniVals["iniinvid"] = $inv->invid;
            $iniVals["iniyear"] = $inv->invyear;
            $iniVals["inimonth"] = $inv->invmonth;
            $iniVals["inipricecnt"] = $iniVals["iniprice"]*$iniVals["inicnt"];
            $invoiceitems->insert($iniVals);
          }
        }
        //aktualizuju cenu v hlavicce fa
        $iniRows = dibi::fetch("SELECT ROUND(SUM(inipricecnt), 2) AS iniprice, SUM(inipricecnt+ROUND((inipricecnt*inivat/100),2)) AS inipricevat FROM invoiceitems WHERE iniinvid=%i", $id);
        //aktualizuju fa
        $iVals = array(
          'invprice' => $iniRows->iniprice,
          'invpricevat' => round($iniRows->inipricevat, 0),
        );
        $invs->update($id, $iVals);
      } catch (Exception $e) {
        $form->addError($e->getMessage());
      }
      $this->redirect('edit', $id);
    }
  }

  protected function createComponentAddForm() {
    $form = new Nette\Application\UI\Form();
    $invs = new \Model\InvoicesModel();

    $form->addText('invic', 'IČ', 40);
    $form->addHidden('invtype', 'othr');
    $form->addText('invmail', 'Email', 40);
    $form->addText('invphone', 'Telefon', 40);

    $form->addSelect('invpaytypid', 'Typ platby', $invs->getEnumPayTypId())
      ->setPrompt("Vyberte ...")
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno.");

    $form->addSubmit('save', 'Vyhledat fakturační údaje a založit fakturu')->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'addFormSubmitted');

    return $form;
  }

  public function addFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $vals = $form->getValues();
      $inv = FALSE;
      //zkusim vyhledat kontaktni udaje
      $fields = "invpaytypid, invrow1, invrow2, invrow3, invrow4, invic, invdic, invphone, invmail, invperiod, invmonth, invyear, invprice, invpricevat";

      if (!empty($vals->invic)) {
        $inv = dibi::fetch("SELECT $fields FROM invoices WHERE invic=%s", $vals->invic, " ORDER BY if(invtype='othr', 0, 1), invdatec DESC LIMIT 1");
      }
      if (!empty($vals->invmail) && $inv == FALSE) {
        $inv = dibi::fetch("SELECT $fields FROM invoices WHERE invmail=%s", $vals->invmail, " ORDER BY if(invtype='othr', 0, 1), invdatec DESC LIMIT 1");
      }
      if (!empty($vals->invphone) && $inv == FALSE) {
        $inv = dibi::fetch("SELECT $fields FROM invoices WHERE invphone=%s", $vals->invphone, " ORDER BY if(invtype='othr', 0, 1), invdatec DESC LIMIT 1");
      }
      if ($inv == FALSE) $inv = array();
      $invs = new \Model\InvoicesModel();
      $date = dibi::fetch("SELECT YEAR(Now()) year, MONTH(Now()) month");

      $inv["invcode"] = $invs->getNextInvCode($date->year, 'othr');
      $inv["invpaytypid"] = $vals->invpaytypid;
      $inv["invtype"] = 'othr';
      $inv["invyear"] = $date->year;
      $inv["invmonth"] = $date->month;
      $inv["invvarsym"] = $invs->getNextVarSym($date->year);
      $inv["invpaydate"] = dibi::fetchSingle("SELECT DATE(NOW() + INTERVAL 14 DAY)");
      $id = $invs->insert($inv);
      $this->redirect("edit", $id);
    }
  }

  protected function createComponentSearchForm() {
    $invs = new \Model\InvoicesModel();
    $form = new Nette\Application\UI\Form();
    $form->addText("name", "Jméno", 10)
      ->setDefaultValue($this->sName);
    $form->addText("phone", "Telefon", 10)
      ->setDefaultValue($this->sPhone);
    $form->addText("mail", "Mail", 10)
      ->setDefaultValue($this->sMail);
    $form->addText("ic", "IČ", 10)
      ->setDefaultValue($this->sIc);
    $form->addSelect("status", "Status", $invs->getEnumInvStatus())
      ->setPrompt('')
      ->setDefaultValue($this->sStatus);
    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vymazat filtr');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sName = Null;
        $this->sPhone = Null;
        $this->sIc = Null;
        $this->sStatus = Null;
        $this->sMail = Null;
      } else {
        $vals = $form->getValues();
        $this->sName = $vals["name"];
        $this->sPhone = $vals["phone"];
        $this->sIc = $vals["ic"];
        $this->sMail = $vals["mail"];
        $this->sStatus = $vals["status"];
      }
    }
    $this->redirect("this");
  }

  Private function getWhere() {
    $where = array();
    if (!empty($this->sName)) {
      array_push($where, (count($where) > 0 ? " AND " : ""), "invrow1 LIKE %~like~", $this->sName);
    }
    if (!empty($this->sPhone)) {
      array_push($where, (count($where) > 0 ? " AND " : ""), "invphone LIKE %~like~", $this->sPhone);
    }
    if (!empty($this->sIc)) {
      array_push($where, (count($where) > 0 ? " AND " : ""), "invic LIKE %~like~", $this->sIc);
    }
    if (!empty($this->sMail)) {
      array_push($where, (count($where) > 0 ? " AND " : ""), "invmail LIKE %~like~", $this->sMail);
    }
    if (!empty($this->sStatus)) {
      array_push($where, (count($where) > 0 ? " AND " : ""), "invstatus=%i", $this->sStatus);
    }
    return $where;
  }


  public function actionEetSend($id) {
    $invs = new InvoicesModel();
    $inv = $invs->load($id);
    $this->sendEet($inv);
    $this->redirect('edit', $id);
  }

  public function sendEet($inv, $setFlashMessage = true) {
    $ret = $this->eetSend($inv);
    if ($setFlashMessage) {
      if ($ret == 'err') {
        $this->flashMessage("Pozor nastala chyba při odeslání do EET.", "err");
      } else if ($ret === 'no') {
        $this->flashMessage("Tato platba se nezasílá do EET");
      } else if ($ret === 'send') {
        $this->flashMessage("Tato platba už byla zaslána do EET");
      } else {
        $this->flashMessage("Odesláno do EET. (FIK: $ret)");
      }
    }
    return $ret;
  }

  public function eetSend($inv, $payMode = null) {
    if (!empty($inv->inveetfik)) {
      return ("send");
    }
    if ($inv->invpaytypid != 2) {
      return ("no");
    }

    $admid = (empty($this->adminData->admid) ? 7 : $this->adminData->admid);
    if (empty($inv->invprovozid)) $inv->invprovozid = 11;
    $eetParams = $params = $this->neonParameters["eet"];
    $eet = new \eet($eetParams);
    $ret = $eet->sendReceipt($inv->invid, $inv->invprovozid, $admid, $inv->invpricevat);
    if ($ret === false) return "err";
    $invs = new InvoicesModel();
    $invs->update($inv->invid, array('inveetfik'=>$ret));
    if ($ret === false) return "err";
    return $ret;
  }



  public function vyfakturujSend() {
    $rows = dibi::fetchAll("SELECT * FROM invoices WHERE invtype=%s", $this->sec, " AND invcode is not null AND coalesce(invvyfid,0)=0 AND date(invdatec)>='2021-02-01'");
    $vyfakturuj = new \VyfakturujCls('<EMAIL>', 'b1rP5D2qwkOe68Y3apkTp6VPQnX0B4oWL3GmV9Zl');
    foreach ($rows as $key => $row) {
      $rows[$key]->items = dibi::fetchAll("SELECT * FROM invoiceitems WHERE iniinvid=%i", $row->invid);
      //$rows[$key]->customer = dibi::fetchAll("SELECT * FROM users WHERE usrid=%i", $row->invusrid);
      $vyfakturuj->postInvoice($rows[$key]);
    }

    $invs = new InvoicesModel();
    if (count($vyfakturuj->ordersToUpdate)) {
      foreach ($vyfakturuj->ordersToUpdate as $invid => $data) {
        $invs->update($invid, $data);
      }
    }
  }


  public function actionDzp() {
    $rows = dibi::fetchAll("SELECT * FROM invoices WHERE invvyfid > 0 AND date(invdatec)>='2021-02-01'");
    $vyfakturuj = new \VyfakturujCls('<EMAIL>', 'b1rP5D2qwkOe68Y3apkTp6VPQnX0B4oWL3GmV9Zl');

    $data = array(
      'date_taxable_supply' => '2021-01-31'
    );

    $cnt = 0;
    foreach ($rows as $key => $row) {
      $vyfakturuj->updateInv($row->invvyfid, $data);
      $cnt++;
    }

    echo "Hotovo $cnt / " .count($rows);
    $this->terminate();

  }




}  
