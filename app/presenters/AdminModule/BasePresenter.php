<?php
namespace AdminModule;
use dibi;
use Nette;

abstract class BasePresenter extends \BasePresenter {
  const NS_LOGIN = 'admin';
  
  protected $admin = null;
  protected $adminData = Null;
  
  /** @persistent */
  public $backlink = '';
  
  /** @persistent */
  public $sec = 'call';
  
  protected function startup() {    
    parent::startup();
     
    // autorizace administratora
    //nactu uzivatele
    $this->admin = $this->getUser();
    $this->user->getStorage()->setNamespace(self::NS_LOGIN);
     
    $this->adminData = false;
    if ($this->admin->isLoggedIn()) {
      $admins = new \Model\AdminsModel();
      $this->adminData = $admins->load($this->admin->id);
    }   
    if ($this->adminData == false) {
      $this->adminData = new \DibiRow(array('admid'=>0));
      $this->admin->logout();
    }
    if ($this->admin->isLoggedIn() && $this->action == "login") {
      $this->redirect('Admin:default');
    } else {  
      if ($this->action != "login") { 
        if (!$this->admin->isLoggedIn()) {
          if ($this->admin->getLogoutReason() === \Nette\Security\IUserStorage::INACTIVITY) {
            $this->flashMessage('Byl/a jste odhlášen/a z důvodu delší neaktivity.');
          }
          $this->redirect('Admin:login');
        }
      }
    }
    $newSec = (string)$this->getParam("changeSec");
    if (!empty($newSec)) $this->sec = $newSec;
  }
  
  protected function beforeRender() {
    //nactu administratora
    $this->template->identity = $this->admin; 
    $this->template->admin = $this->adminData;
    
    $this->template->sec = $this->sec; 
    parent::beforeRender();
  }
  
  protected function saveImage($image, $path, $fileName, $sizes) {
    //upravim a ulozim obrazek
    if (isset($image) && is_array($sizes)) {
      foreach ($sizes as $size) {
        $img = clone $image;
        list($w, $h, $dir) = explode("x", $size);
        if ($w > 0 || $h > 0) {    
          $image->resize($w, $h); // resize, který prostor vyplní a možná překročí
          //doplnim na dany rozmer bilou
          $blank = Nette\Image::fromBlank($w, $h, Nette\Image::rgb(255, 255, 255));
          $blank->place($image,'50%', '50%');
          $path = $path.($dir!="" ? '/'.$dir : '').'/';    
          $blank->save($path.$fileName, 100, Nette\Image::JPEG);
          chmod($path.$fileName, 0777);
        }  
      }        
    }
  }
  
  public function createComponentTexylaJs() {
    $files = new \WebLoader\FileCollection(WWW_DIR . '/texyla');
    $files->addFiles(array(
      // core
      "js/texyla.js",
      "js/selection.js",
      "js/texy.js",
      "js/buttons.js",
      "js/dom.js",
      "js/view.js",
      "js/ajaxupload.js",
      "js/window.js",

      // languages
      "languages/cs.js",

      // plugins
      "plugins/keys/keys.js",
      "plugins/resizableTextarea/resizableTextarea.js",
      "plugins/img/img.js",
      "plugins/table/table.js",
      "plugins/link/link.js",
      "plugins/emoticon/emoticon.js",
      "plugins/symbol/symbol.js",
      "plugins/files/files.js",
      "plugins/color/color.js",
      "plugins/textTransform/textTransform.js",
      "plugins/youtube/youtube.js",
      "plugins/gravatar/gravatar.js",
    
      //init file
      WWW_DIR . "/js/texyla-admin-init.js"
    ));
    
    $compiler = \WebLoader\Compiler::createJsCompiler($files, WWW_DIR . '/webtemp');
    
    $filter = new \WebLoader\Filter\VariablesFilter(array(
      "baseUri" => $this->getHttpRequest()->url->baseUrl,
      "previewPath" => $this->link("Texyla:preview"),
      "filesPath" => $this->link("Texyla:listFiles"),
      "filesUploadPath" => $this->link("Texyla:upload"),
      "filesMkDirPath" => $this->link("Texyla:mkDir"),
      "filesRenamePath" => $this->link("Texyla:rename"),
      "filesDeletePath" => $this->link("Texyla:delete"),
    ));
    $compiler->addFilter($filter);

    return new \WebLoader\Nette\JavaScriptLoader($compiler, $this->template->basePath . '/webtemp'); 
  }
  
  protected function formatNumber($value) {
    $value = str_replace(' ', '', $value);
    return str_replace(',', '.', $value);
  }
  protected function timeToMinutes($time) {
      $time = explode(':', $time);
      if (count($time) === 3) {
        return ($time[0] * 60) + ($time[1]) + ($time[2] / 60);
      } elseif (count($time) === 2) {
        return ($time[1] + ($time[2] / 60));
      }
  }

  protected function formatDate($value) {
    $value = (string)$value;
    if (!empty($value)) {
      $value = str_replace('. ', '.', $value);
      $arr = explode('.', $value);
      if (strlen($arr[2]) == 2) $arr[2] = '20'.$arr[2];
      $value = $arr[2].'-'.$arr[1].'-'.$arr[0]; 
    }  
    return ($value);
  }
  
  protected function formatDateFromMySql($value) {
    $value = (string)$value;
    if (!empty($value)) {
      $arr = explode(' ', $value);
      $arr = explode('-', $arr[0]);
      $value = $arr[2].'.'.$arr[1].'.'.$arr[0]; 
    }  
    return ($value);
  }
  
  protected function formatDateTime($value) {
    $value = (string)$value;
    if (!empty($value)) {
      $value = str_replace('. ', '.', $value);
      $arr = explode(' ', $value);
      $date = $arr[0];
      $time = $arr[1];
      $arr = explode('.', $date);
      if (strlen($arr[2]) == 2) $arr[2] = '20'.$arr[2];
      $value = $arr[2].'-'.$arr[1].'-'.$arr[0].' '.$time; 
    }  
    return ($value);
  }
  
  protected function csvParse($str, $f_delim = ";", $r_delim = "\n", $qual = '"') {
    $output = array();
    $row = array();
    $word = '';

    $len = strlen($str);
    $inside = false;

    $skipchars = array($qual);

    for ($i = 0; $i < $len; ++$i) {
      $c = (!empty($str[$i]) ? $str[$i] : '');
      if (!$inside && $c == $f_delim) {
        $row[] = $word;
        $word = '';
      } elseif (!$inside && $c == $r_delim) {
        $row[] = $word;
        $word = '';
        $output[] = $row;
        $row = array();
      //} else if ($inside && in_array($c,$skipchars) && ($i+1 < $len && $str[$i+1] == $qual)) {
      //  $word .= $qual;
      //  ++$i;
      } else if ($c == $qual) {
        $str = (!empty($str[$i-1]) ? $str[$i-1] : '');
        if ($str==$f_delim || (count($row)==0 && $word == '')) {
          $inside=true;
        } else if ($str[$i+1]==$f_delim) {
          $inside=false;
        } else {
          $word .= $qual;
        }
        //$inside = !$inside;
      } else {
        $word .= $c;
      }
    }

    $row[] = $word;
    $output[] = $row;

    return $output;
  }

  protected function getBankAccounts() {

    return array(
      '**********/2010' => 'FIO : **********/2010',
      '*********/5500' => 'RB : *********/5500',
      '**********/2010' => 'FIO osobní: **********/2010'
    );
  }
}  
?>
