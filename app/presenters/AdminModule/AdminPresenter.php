<?php
namespace AdminModule;
use dibi;
use Nette;

final class AdminPresenter extends BasePresenter {
  
  public function actionLogin() {

  }
  
  public function actionLogout() {
    $this->admin->logout();
    $this->flashMessage("Odhlášení proběhlo ú<PERSON>ě.");
    $this->redirect('Admin:login');
  }  

  public function loginFormSubmitted(Nette\Application\UI\Form $form) {
    try {
      $this->admin->login($form['admmail']->getValue(), $form['admpassw']->getValue(), self::NS_LOGIN);
      $this->admin->setExpiration('14 days', FALSE);
      $this->restoreRequest($this->appSession->backlink);
      $this->redirect('Admin:default');

    } catch (Nette\Security\AuthenticationException $e) {
      $form->addError($e->getMessage());
    }
  }
  
  
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    
    $name = $this->getParam('name');
    $varSym = $this->getParam('varsym');
    $where = array();
    if (!empty($name)) {
      $where[] = "(invrow1 like '%$name%' OR invrow2 like '%$name%')";
      $filterText = "Příjmení, název firmy: $name,";
    }  
    if (!empty($varSym)) {
      $where[] = "(invvarsym like '$varSym%')";
      $filterText = " Variabilní symbol: $name,";
    }  
    if (count($where) > 0) {
      $sql = "SELECT *, DATEDIFF(CURDATE(),invpaydate) AS invduedateafter FROM invoices WHERE invstatus=0 and invtype='$this->sec'";
      foreach ($where as $value) {
        $sql .= "AND ".$value;  
      }
      $this->template->invRows = dibi::fetchAll($sql);  
      $this->template->filterText = trim($filterText, ',');  
    }

    if ($this->sec == 'call') {
      //doplnim pozadavky na zmenu tarifu
      $plis = new \Model\PricelistsModel();
      $this->template->enum_pliid = $plis->getEnumPliId();
      $this->template->tacs = dibi::fetchAll("SELECT * FROM tarifchanges INNER JOIN users ON (usrid=tacusrid) WHERE tacstatus=0");
    }
    
    //doplnim nejvetsi neplatice
    $this->template->notPayedAfter = dibi::fetchAll("
    SELECT COUNT(invid) AS cnt, usrname, usrphone, MAX(invpaydate) invpaydate_max, 
    MAX(DATEDIFF(CURDATE(),invpaydate)) AS invduedateafter,
    SUM(invpricevat) AS invpricevat, usrid
    FROM invoices 
    INNER JOIN users ON (invusrid=usrid) 
    WHERE invstatus=0 AND
    invtype=%s", $this->sec, " AND
    (invcode IS NOT NULL OR (invcode IS NULL AND usrpayrateid=0)) AND 
    (DATEDIFF(CURDATE(),invpaydate) >= 15) 
    GROUP BY usrid
    ORDER BY invduedateafter DESC
    ");
    
  }
  
  /********************* view add, edit *********************/ 
  
  public function renderEditSelf() {
    $identity = \Nette\Environment::getUser()->setNamespace(self::NS_LOGIN);
    $this->renderEdit($identity->id);
  }
    
  public function renderEdit($id) {
    $form = $this['adminForm'];
    
    if (!$form->isSubmitted()) {
      $admin = new \Model\AdminsModel();
      $row = $admin->load($id);
      if (!$row) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
      $form->setDefaults($row);
    }
  }
  
  public function adminFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $id = (int) $this->getParam('id');
      $admin = new \Model\AdminsModel();
      if ($id > 0) {
        $admin->update($id, $form->getValues());
        $this->flashMessage('Údaje byly aktualizovány.');
      } else {
        $id = $admin->insert($form->getValues());
        $this->flashMessage('Účet byl vytvořen.');
      }
    }
    $this->redirect('default');
  }
  
  /********************* facilities *********************/

  /**
   * Component factory.
   * @param  string  component name
   * @return void
   */
  protected function createComponentAdminLoginForm() {
    $form = new Nette\Application\UI\Form;
    $form->addText('admmail', 'Přihlašovací jméno:')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Vaše přihlašovací jméno (email).');

    $form->addPassword('admpassw', 'Heslo:')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.');

    $form->addSubmit('login', 'Přihlásit se');
    $form->onSuccess[] = array($this, 'loginFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }
  
  protected function createComponentAdminForm() {
    $id = $this->getParam('id');
        
    $admin = new \Model\AdminsModel();
    $form = new Nette\Application\UI\Form;
    $form->addGroup('Přihlašovací údaje');
    $form->addText('admmail', 'Váš email:', 40, $admin->getColProperty('admmail', 'size'))
      ->setEmptyValue('@')
      ->setOption('description', ' slouží zároveň jako přihlašovací jméno')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');
    if (empty($id)) {
      $form->addPassword('admpassw', 'Heslo:')
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.');      
      $form->addPassword('admpassw2', 'Heslo podruhé:')
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo podruhé.')
        ->addCondition(Nette\Forms\Form::FILLED) 
          ->addRule(Nette\Forms\Form::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["admpassw"]);
    }
    
    $identity = \Nette\Environment::getUser()->setNamespace(self::NS_LOGIN);
    if ($identity->isAllowed('Admin:Admin', 'edit')) {
      $form->addSelect('admrole', 'Role', $admin->gete);      
      $form->addSelect('admstatus', 'Status', EnumsModel::getEnum(EnumsModel::ENUM_ADMINS_STATUS));
    }
    
    
    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');
    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = callback($this, 'adminFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }
  
  protected function createComponentSearchNotPayedInvoiceForm() {
    $form = new Nette\Application\UI\Form;
    $form->addText('varsym', 'Variabilní symbol:');
    $form->addText('name', 'Příjmení zákazníka, název firmy:');
    
    $form->addSubmit('search', 'Hledat neuhrazené výzvy k platbě');
    $form->onSuccess[] = array($this, 'searchNotPayedInvoiceFormSubmitted');
    return $form;
  }
  
  public function searchNotPayedInvoiceFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form['search']->isSubmittedBy()) {
      $params = $form->getValues();
      $vals = array();
      if (!empty($params["name"])) $vals["name"] = $params["name"];
      if (!empty($params["varsym"])) $vals["varsym"] = $params["varsym"];
    }
    $this->redirect('default', $vals);
  }
}  
?>