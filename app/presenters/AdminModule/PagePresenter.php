<?php
namespace AdminModule;
use dibi;
use Nette;

final class PagePresenter extends BasePresenter {
  
  public function editFormSubmitted (Nette\Application\UI\Form $form) {
    
    if ($form->isSubmitted()) {
      
      $page = new \Model\PagesModel();
      $id = (int)$this->getParam('id');
      $vals = $form->getValues(); 
    
      if (empty($vals["pagurlkey"])) $vals["pagurlkey"] = \Nette\Utils\Strings::webalize($vals["pagname"]);
    
      //projdu formularova pole a ktere nejsou treba odstranim
      foreach ($vals as $key => $value) {
      }                                         

      try {
        if ($page->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
          $this->redirect('Page:default');        
        }
        
      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }    
    }
  } 
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    $page = new \Model\PagesModel();
    $dataRows = dibi::query("SELECT * FROM pages ORDER BY pagname")
      ->fetchAssoc('pagid');
    
    $this->template->dataRows = $dataRows;
    $this->template->enum_pagtypid = $page->getEnumPagTypId();  
  }
  
  public function renderEdit($id) {
    $form = $this['editForm'];
    
    if (!$form->isSubmitted()) {
      $page = new \Model\PagesModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $page->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      }
      $this->template->dataRow = $dataRow; 
      $this->template->id = $id; 
      
      $this->template->enum_pagtypid = $page->getEnumPagTypId();
    }
  }
  
  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParam('id');
    
    $page = new \Model\PagesModel();
    
    $form = new Nette\Application\UI\Form();
    
    $form->addSelect('pagtypid', 'Typ stránky:', $page->getEnumPagTypId());
    
    $form->addText('pagname', 'Název:', 30, $page->getColProperty('pagname', 'size'))
      ->addRule(Nette\Forms\Form::FILLED, 'Název: je nutné vyplnit');
    
    $form->addText('pagurlkey', 'URL:', 30, $page->getColProperty('pagurlkey', 'size'));
      
    $form->addText('pagkeywords', 'Klíčové slova:', 100, $page->getColProperty('pagkeywords', 'size'));  
    
    $form->addTextArea('pagbody', 'Text:', 60, 20, $page->getColProperty('pagdesc', 'size'));
    $form['pagbody']->getControlPrototype()->class('texyla'); 
    
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
  } 
}  
?>