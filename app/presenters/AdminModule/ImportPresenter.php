<?php
namespace AdminModule;
use dibi;
use Model\PricelistsModel;
use Model\UsersModel;
use Nette;
use <PERSON><PERSON><PERSON>\SimpleCSV;
use <PERSON>\Debugger;
use <PERSON><PERSON><PERSON>\SimpleXLSX;

final class ImportPresenter extends BasePresenter {
  
  public function importSubmitted (Nette\Application\UI\Form $form) {
    $fileName = "";
    $log = array();
    $this->flashMessage("Start: ".date('H:i:s'));
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      
      //zjistim jestli negeneruje data do budoucnosti
      $r = dibi::fetch("SELECT YEAR(CURDATE()) AS year, MONTH(CURDATE()) AS month");
      if (($r->year == $vals["year"] && $r->month <= $vals["month"]) || $r->year < $vals["year"]) {
        $err = true;
        $form->addError("Nemůžete importovat data pro aktuální nebo budouc<PERSON> mě<PERSON>.(".$vals["month"]."/".$vals["year"].")");
        return;
      }      
      
      //kontrol zda uz pro dany rok a mesic neni volani
      $itemsCnt = (int)dibi::fetchSingle("SELECT COUNT(*) FROM calls WHERE calmonth=%i", $vals["month"]," AND calyear=%i", $vals["year"]);
      if ($itemsCnt > 0) {
        $form->addError("Import pro tento rok a měsíc už byl proveden!", 'err');
        return;
      }
      
        
      if (!$vals["imp_file"][0]->isOk()) {
        $form->addError("Neplatný importní soubor.");
        return;
      } else {
        $fileName = $vals["imp_file"][0]->getTemporaryFile();
      }
      
      //zjistim kodovani souboru
      $file = file_get_contents($fileName);
      $encoding = "";
      if (preg_match('#[\x80-\x{1FF}\x{2000}-\x{3FFF}]#u', $file)) {
        $encoding = 'UTF-8'; 
      } else if (preg_match('#[\x7F-\x9F\xBC]#', $file)) {
        $encoding = 'cp1250'; 
      } else {  
        $encoding = 'ISO-8859-2';
      }
     
      $csv = new \parseCSV();
      $csv->encoding($encoding, 'UTF-8');
      $csv->delimiter = ";";
      $csv->parse($fileName);
      
      $cnt = 0;
      $cntinsER = 0;
      //$calls = new \Model\CallsModel();
      $arr = array();

      $vatOper = 1+($this->config["INVOICE_DPHLEVEL_0"]/100);
      foreach ($csv->data as $key => $row) {
        if (trim($row[2]) == 'Počet registrovaných sledovaných hovorů') continue; 
        if (trim($row[2]) == 'Součet') continue; 
        $cnt++;
        $arr["calnumber"] =  substr('420'.trim($row[0]), -12);
        $arr["calmonth"] = $vals["month"];
        $arr["calyear"] = $vals["year"];
        $arr["caldir"] = Null;
        $arr["calinvoice"] = Null;
        $arr["caltargetnumber"] = trim($row[2]);
        $arr["calservice"] = trim($row[1]);
        $arr["caldatetime"] = $this->formatDateTime(trim($row[3]).' '.$row[4]);
        $arr["calduration"] = trim($row[5]);
        $arr["caldurationpayed"] = trim($row[5]);
        $arr["caldata"] = 0;
        $arr["calpayvalue"] = 0;
        $arr["calunit"] = $row[6];
        
        $ret = mysql_query("
        INSERT INTO `calls`
        (`calid`,`calinvoice`,`calmonth`,`calyear`,`calnumber`,`caldir`,`calservice`,`caldatetime`,`caltargetnumber`,`calduration`,`caldurationpayed`,`caldata`,`calpayvalue`,`calunit`,`caldatec`,`caldateu`) VALUES 
        ( NULL,'".$arr["calinvoice"]."','".$arr["calmonth"]."','".$arr["calyear"]."','".$arr["calnumber"]."','".$arr["caldir"]."','".$arr["calservice"]."','".$arr["caldatetime"]."','".$arr["caltargetnumber"]."',".$arr["calduration"].",".$arr["caldurationpayed"].",".$arr["caldata"].",".$arr["calpayvalue"].",'".$arr["calunit"]."',NOW(),NULL)");
        if ($ret === FALSE) {
          $this->flashMessage("Err: ".print_r($arr, true), 'err');
          $cntinsER++;
        }  
        
      }
      $this->flashMessage("Import dokončen: ".date('H:i:s')."(nové záznamy: $cnt bez chyb, $cntinsER chyb)");
      $this->redirect('default');
    }
  }
  
  public function importInvItemsSubmitted (Nette\Application\UI\Form $form) {
    $usrs = new \Model\UsersModel();
    $fileName = "";
    $log = array();
    $numberitems = new \Model\NumberitemsModel();
    $plis = new \Model\PricelistsModel();
    
    $vals = $form->getValues();
    
    //zjistim jestli negeneruje data do budoucnosti
    $r = dibi::fetch("SELECT YEAR(CURDATE()) AS year, MONTH(CURDATE()) AS month");
    if (($r->year == $vals["year"] && $r->month <= $vals["month"]) || $r->year < $vals["year"]) {
      $err = true;
      $form->addError("Nemůžete importovat data pro aktuální nebo budoucí měsíce.(".$vals["month"]."/".$vals["year"].")");
      return;
    }
    
    if (!$vals["imp_file"][0]->isOk()) {
      $form->addError("Neplatný importní soubor.");
      return;
    } else {
      $fileName = $vals["imp_file"][0]->getTemporaryFile();
    }
    
    libxml_use_internal_errors(true);
    //zjistim kodovani souboru
    $file = file_get_contents($fileName);
    $encoding = "";
    if (preg_match('#[\x80-\x{1FF}\x{2000}-\x{3FFF}]#u', $file)) {
      $encoding = 'UTF-8'; 
    } else if (preg_match('#[\x7F-\x9F\xBC]#', $file)) {
      $encoding = 'cp1250'; 
    } else {  
      $encoding = 'ISO-8859-2';
    }
    $items = simplexml_load_file($fileName);
    
    if (!$items) {
      foreach (libxml_get_errors() as $error) {
        $form->addError($error->message);
      }
      libxml_clear_errors();
      return FALSE;
    }
    
    $node = 'cz.simplycom.system.xml.PhoneNumberXml';
    
    if ($form["check"]->submittedBy) {
      //kontrola zda ceniky u aktivnich cisel jsou platne
      //nactu pouzite ceniky
      $err = false;
      $rows = dibi::fetchAll("SELECT numpliid, numnumber FROM numbers INNER JOIN users ON (usrid=numusrid) WHERE usrstatus IN (0,2) GROUP BY numpliid");
      foreach ($rows as $key => $row) {
        $pli = $plis->load($row->numpliid);
        if ($pli) {
          if ($pli->plistatus == 1) {
            $err = true; 
            $form->addError("U čísla ".$row->numnumber." je pouzitý ceník ID=".$row->numpliid." který je blokovaný");
          }  
        } else {
          $err = true;
          $form->addError("Ceník ID=".$row->numpliid." neexistuje nebo není u čísla ".$row->numnumber." vyplněný");
        }
      }
      
      foreach ($items->phoneNumbers->$node as $item) {
        $iniNumber = '420'.(string)$item->phoneNumber;

        //zjistim cenik
        $pli = dibi::fetch("
          SELECT plifee, numusrid, numdatefrom, numdateto, plifreesms, plifreemms, plifreemin 
          FROM numbers 
              INNER JOIN pricelists ON (pliid=numpliid) 
          WHERE pliserid='tmobile' AND numnumber=%s", $iniNumber
        );

        if ($pli == FALSE) {
          $err = TRUE;
          $form->addError("Telefonní číslo $iniNumber nebylo nalezeno v databázi, nebo nemá přiřazený ceník. Doplňte jej a proveďte znovu import.");
          continue;
        }
        $usr = $usrs->load($pli->numusrid);
        if ($usr->usrstatus == 1) {
          $err = TRUE;
          $form->addError("Zákazník $usr->usrname s číslem $iniNumber je blokovaný.");
        }
      }
      if (!$err) {
        $this->flashMessage("Vše OK"); 
        $this->redirect("Import:default");
      } else {
        return;
      }  
      
    } else if ($form["save"]->submittedBy) {
      $this->flashMessage("Start: ".date('H:i:s'));
      
      //kontrola zda ceniky u aktivnich cisel jsou platne
      //nactu pouzite ceniky
      $err = false;
      $rows = dibi::fetchAll("SELECT numpliid, numnumber FROM numbers INNER JOIN users ON (usrid=numusrid) WHERE usrstatus IN (0,2) GROUP BY numpliid");
      foreach ($rows as $key => $row) {
        $pli = $plis->load($row->numpliid);
        if ($pli) {
          if ($pli->plistatus == 1) {
            $err = true; 
            $this->flashMessage("U čísla ".$row->numnumber." je pouzitý ceník ID=".$row->numpliid." který je blokovaný", 'err');
          }  
        } else {
          $err = true;
          $this->flashMessage("Ceník ID=".$row->numpliid." neexistuje nebo není u čísla ".$row->numnumber." vyplněný");
        }
      }
      if ($err) $this->redirect("Import:default");
      
      //vymazu pokud tam neco uz je pro dane obdobi
      dibi::query("DELETE FROM numberitems WHERE nniserid='tmobile' AND nnimonth=%i", $vals["month"]," AND nniyear=%i", $vals["year"]);
      
      $cnt = 0;
      $cntinsER = 0;
      //$calls = new \Model\CallsModel();
      $arr = array();

      $vatOper = 1+($this->config["INVOICE_DPHLEVEL_0"]/100);
      
      $findNumber = true;
      $iniNumber = "";
      $monthDays = cal_days_in_month(CAL_GREGORIAN, $vals["month"], $vals["year"]);
      
      foreach ($items->phoneNumbers->$node as $item) {
        $iniNumber = '420'.(string)$item->phoneNumber;

        //vlozim polozku za pausal pro dane cislo
        //zjistim cenik
        $pli = dibi::fetch("
          SELECT plifee, numusrid, numdatefrom, numdateto, plifreesms, plifreemms, plifreemin 
          FROM numbers 
              INNER JOIN pricelists ON (pliid=numpliid) 
          WHERE pliserid='tmobile' AND numnumber=%s", $iniNumber
        );

        if ($pli == FALSE) {
          $this->flashMessage("Telefonní číslo $iniNumber nebylo nalezeno v databázi, nebo nemá přiřazený ceník nebo účet zákazníka je blokovaný.", "err");
          $this->redirect("Import:default");
        }
        $usr = $usrs->load($pli->numusrid);
        if ($usr->usrstatus == 1) {
          $this->flashMessage("Zákazník $usr->usrname s číslem $iniNumber je blokovaný.", "err");
          $this->redirect("Import:default");
        }
                
        $plifee = $pli->plifee;
        //vychazim z ceny bez DPH
        $plifee = (DOUBLE)round(($plifee / (1 + ($this->config["INVOICE_DPHLEVEL_0"]/100))), 2);
        $dateFromIsIn = False;
        $dateToIsIn = False;
        $days = $monthDays;
        $priTextAdd = "";
        
        //zjistim jestli mam slevnit tarif
        if (!empty($pli->numdatefrom)) {
          //zjistim jestli to je tento rok a mesic
          $dateFromIsIn = (bool)dibi::fetchSingle("SELECT MONTH('$pli->numdatefrom')=".$vals["month"]." AND YEAR('$pli->numdatefrom')=".$vals["year"]);
          if (!$dateFromIsIn) {
            //Zjistim jestli je jeste tarif platny
            $isValid = (bool)dibi::fetchSingle("
            SELECT (MONTH('$pli->numdatefrom')<=".$vals["month"]." AND YEAR('$pli->numdatefrom')=".$vals["year"].") OR
            YEAR('$pli->numdatefrom')<".$vals["year"]
            );
            if (!$isValid) continue;
          }
        } 
        if (!empty($pli->numdateto)) {
          //zjistim jestli to je tento rok a mesic
          $dateToIsIn = (bool)dibi::fetchSingle("SELECT MONTH('$pli->numdateto')=".$vals["month"]." AND YEAR('$pli->numdateto')=".$vals["year"]);
          if (!$dateToIsIn) {
            //Zjistim jestli je jeste tarif platny
            $isValid = (bool)dibi::fetchSingle("
            SELECT (MONTH('$pli->numdateto')>=".$vals["month"]." AND YEAR('$pli->numdateto')=".$vals["year"].") OR
            YEAR('$pli->numdateto')>".$vals["year"]
            );
            if (!$isValid) continue;
          }
        }
        If ($dateFromIsIn && $dateToIsIn) {
          //obe data spadaji do uctovaciho obdobi
          $before = new \DateTime($pli->numdatefrom);
          $days = (int)$before->diff(new \DateTime($pli->numdateto))->format('%a days');
        } else If ($dateFromIsIn) {
          $before = new \DateTime($pli->numdatefrom);
          $days = (int)$before->diff(new \DateTime($vals["year"].'-'.$vals["month"].'-'.$monthDays))->format('%a');
        } else If ($dateToIsIn) {
          $before = new \DateTime($vals["year"].'-'.$vals["month"].'-01');
          $days = (int)$before->diff(new \DateTime($pli->numdateto))->format('%a');
        }                       
        if ($days < $monthDays) {
          //pocitam slevu z tarifu polde dni
          $plifeeFull = $plifee;
          $plifee = round(($plifeeFull * $days / $monthDays), 2);
          $disc = round(($plifeeFull - $plifee) * 100 / $plifeeFull, 0); 
          $priTextAdd = " (za $days dní)";
        }

        $iVals = array(
          'nniserid'=> 'tmobile',
          'nnimonth'=> $vals["month"],
          'nniyear'=> $vals["year"],
          'nninumber'=> $iniNumber,
          'nnivat'=> $this->config["INVOICE_DPHLEVEL_0"],
          'nnicnt'=> 1,
          'nniprice' => $plifee,
          'nnipricecnt' => $plifee,
          'nnitext' => 'Paušál pro číslo '.$iniNumber.$priTextAdd,
        );
        $numberitems->insert($iVals); 
        $cnt++;

        //vlozim polozky
        $node = 'cz.simplycom.system.xml.InvoiceLineXml';
        foreach ($item->lines->$node as $row) {
          //mesicni pausal u cisla preskocim
          $text = (string)$row->text;

          if ($text == 'Měsíční paušál za tarif') continue;  
          //podnikova sit u cisla preskocim
          if ($text == 'Podniková síť') continue;  
          //aktivace podnikove site u cisla preskocim
          if ($text == 'Aktivace Podnikové sítě') continue;  

          //spocitam uctovane jednotky
          $a = (string)$row->amount;
          $aArr= explode(':', $a);
          $arr = array();
          //zapisu jednotky zdarma
          if ($text == 'MMS národní sítě') $arr["nnicntfree"] = (int)$pli->plifreemms;
          if ($text == 'SMS národní sítě') $arr["nnicntfree"] = (int)$pli->plifreesms;
          if ($text == 'Volání mobilní a pevné síte ČR') $arr["nnicntfree"] = (int)$pli->plifreemin*60;
          if (count($aArr) == 3) {
            //musim prevest na vteriny
            list($hours, $minutes, $seconds) = $aArr; 
            $arr["nnicnt"] = isset($seconds) ? (int)$hours * 3600 + (int)$minutes * 60 + (int)$seconds : (int)$hours * 60 + (int)$minutes;
            $arr["nniunit"] = 's';
          } else {
            $arr["nnicnt"] = (double)$a;
          }
          //if (!empty($arr["nnicntfree"])) $arr["nnicnt"] = $arr["nnicntfree"] - $arr["nnicnt"]; 
          $arr["nnimonth"] = $vals["month"];
          $arr["nniyear"] = $vals["year"];
          $arr["nnivat"] =  (int)$row->vat;
          $arr["nninumber"] =  $iniNumber;
          $arr["nniprice"] = 0;
          $price = (double)$row->price;

          //doplním cenu za navýšení dat balíčků
          if (strpos($text, 'Navýšení datového limitu') === 0) {
            $valueStr = trim(mb_substr($text, 25));
            //1GB 119kč, 2,5GB 169kč, 4GB 219kč, 8GB 329kč
            $value = 0;
            switch ($valueStr) {
              case '400MB':
                $value = 66.12;
                break;
              case '1GB':
                $value = 98.35;
                break;
              case '2.5GB':
                $value = 139.67;
                break;
              case '4GB':
                $value = 180.99;
                break;
              case '8GB':
                $value = 271.9;
                break;
            }

            if ($value > 0) {
              $price = $value;
            }
          }

          $tax = (double)$row->tax;  
          $arr["nnipricecnt"] = $price; //cena bez DPH
          $arr["nnitext"] = $text;
          $arr["nniserid"] = 'tmobile';
          
          $id = $numberitems->insert($arr);   
          if ($id === FALSE) {
            $this->flashMessage("Err: ".print_r($arr, true), 'err');
            $cntinsER++;
          }
        }         
      }
      $this->flashMessage("Import dokončen: ".date('H:i:s')."(nové záznamy: $cnt bez chyb, $cntinsER chyb)");
      $this->redirect('default');
    }
  }
  
  
  public function renderDefault() {
    //nactu si cisla co nejsou 
      
  }
  
  /********************* facilities *********************/

  protected function createComponentImportForm() {
  
    $form = new Nette\Application\UI\Form();
    $model = new \Model\CallsModel();
    $form->addUpload('imp_file', "Importní soubor", 100)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte importní soubor");
    
    $month = (int)date('m')-1;
    $year = (int)date('Y');
    if ($month == 0) {
      $month = 12;
      $year = $year - 1;
    }
    
    $form->addSelect('month', 'Měsíc', $model->getEnumMonId())
      ->setDefaultValue($month)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte měsíc"); 
      
    $form->addText('year', 'Rok', 4)
    ->setDefaultValue($year)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte rok"); 
    
    $form->addSubmit('save', 'Importovat');    
    $form->onSuccess[] = array($this, 'importSubmitted');
    return $form;  
  }
  
  protected function createComponentImportInvItemsForm() {
  
    $form = new Nette\Application\UI\Form();
    $model = new \Model\CallsModel();
    $form->addUpload('imp_file', "Importní soubor", 100)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte importní soubor");
    
    $month = (int)date('m')-1;
    $year = (int)date('Y');
    if ($month == 0) {
      $month = 12;
      $year = $year - 1;
    }
    
    $form->addSelect('month', 'Měsíc', $model->getEnumMonId())
      ->setDefaultValue($month)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte měsíc"); 
      
    $form->addText('year', 'Rok', 4)
    ->setDefaultValue($year)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte rok"); 
    
    $form->addSubmit('check', 'Kontrolovat chybějící čísla');    
    $form->addSubmit('save', 'Importovat');    
    $form->onSuccess[] = array($this, 'importInvItemsSubmitted');
    return $form;  
  }

  protected function createComponentImportO2InvItemsForm() {

    $form = new Nette\Application\UI\Form();
    $model = new \Model\CallsModel();
    $form->addUpload('imp_file', "Importní soubor", 100)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte importní soubor - XML formát!");

    $month = (int)date('m')-1;
    $year = (int)date('Y');
    if ($month == 0) {
      $month = 12;
      $year = $year - 1;
    }

    $form->addSelect('month', 'Měsíc', $model->getEnumMonId())
      ->setDefaultValue($month)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte měsíc");

    $form->addText('year', 'Rok', 4)
    ->setDefaultValue($year)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte rok");

    $form->addSubmit('check', 'Kontrolovat chybějící čísla');
    $form->addSubmit('save', 'Importovat');
    $form->onSuccess[] = array($this, 'importO2InvItemsSubmitted');
    return $form;
  }

  public function importO2InvItemsSubmitted (Nette\Application\UI\Form $form) {
    $usrs = new \Model\UsersModel();
    $fileName = "";
    $log = array();
    $numberitems = new \Model\NumberitemsModel();
    $plis = new \Model\PricelistsModel();

    $vals = $form->getValues();

    //zjistim jestli negeneruje data do budoucnosti
    $r = dibi::fetch("SELECT YEAR(CURDATE()) AS year, MONTH(CURDATE()) AS month");
    if (($r->year == $vals["year"] && $r->month <= $vals["month"]) || $r->year < $vals["year"]) {
      $err = true;
      $form->addError("Nemůžete importovat data pro aktuální nebo budoucí měsíce.(".$vals["month"]."/".$vals["year"].")");
      return;
    }

    if (!$vals["imp_file"][0]->isOk()) {
      $form->addError("Neplatný importní soubor.");
      return;
    } else {
      $fileName = $vals["imp_file"][0]->getTemporaryFile();
    }

    //zjistim kodovani souboru

    $xml = new \SimpleXMLElement(file_get_contents($fileName), LIBXML_COMPACT | LIBXML_PARSEHUGE);

    //z feedu načtu všechny tel čísla
    $phoneNumbers = [];

    foreach ($xml->subscribers->subscriber as $subscriber) {
      $attributes = $subscriber->attributes();
      $phoneNumbers[] = '420'.(string)$attributes->phoneNumber;
    }

    //$phoneNumbers = ["420777562544" => "420777562544", "420777596978" => "420777596978"];

    //jen kontrola
    if ($form["check"]->isSubmittedBy()) {

      //kontrola ceniků a čísel
      $errors = $this->checkPricelists($phoneNumbers, 'o2');

      if (count($errors) === 0) {
        $this->flashMessage("Vše OK");
        $this->redirect("Import:default");
      } else {
        foreach ($errors as $error) {
          $form->addError($error);
        }
        return false;
      }

    } else if ($form["save"]->isSubmittedBy()) {

      //kontrola ceniků a čísel
      $errors = $this->checkPricelists($phoneNumbers, "o2");

      if (count($errors) > 0) {
        foreach ($errors as $error) {
          $form->addError($error);
        }
        return false;
      }

      $this->flashMessage("Start: ".date('H:i:s'));

      //vymazu pokud tam neco uz je pro dane obdobi
      dibi::query("DELETE FROM numberitems WHERE nniserid='o2' AND nnimonth=%i", $vals["month"]," AND nniyear=%i", $vals["year"]);

      $cnt = 0;
      $cntinsER = 0;
      //$calls = new \Model\CallsModel();
      $arr = array();

      $vatOper = 1+($this->config["INVOICE_DPHLEVEL_0"]/100);

      $findNumber = true;
      $iniNumber = "";
      $monthDays = cal_days_in_month(CAL_GREGORIAN, $vals["month"], $vals["year"]);

      foreach ($xml->subscribers->subscriber as $subscriber) {
        $iniNumber = '420'.(string)$subscriber->attributes()->phoneNumber;

        /*
        if (!array_key_exists($iniNumber, $phoneNumbers)) {
          continue;
        }
        */

        //vlozim polozku za pausal pro dane cislo
        //zjistim cenik
        $pli = dibi::fetch("
          SELECT plifee, numusrid, numdatefrom, numdateto, plifreesms, plifreemms, plifreemin 
          FROM numbers 
              INNER JOIN pricelists ON (pliid=numpliid) 
          WHERE pliserid='o2' AND numnumber=%s", $iniNumber
        );

        $plifee = $pli->plifee;
        //vychazim z ceny bez DPH
        $plifee = (DOUBLE)round(($plifee / (1 + ($this->config["INVOICE_DPHLEVEL_0"]/100))), 2);
        $dateFromIsIn = False;
        $dateToIsIn = False;
        $days = $monthDays;
        $priTextAdd = "";

        //zjistim jestli mam slevnit tarif
        if (!empty($pli->numdatefrom)) {
          //zjistim jestli to je tento rok a mesic
          $dateFromIsIn = (bool)dibi::fetchSingle("SELECT MONTH('$pli->numdatefrom')=".$vals["month"]." AND YEAR('$pli->numdatefrom')=".$vals["year"]);
          if (!$dateFromIsIn) {
            //Zjistim jestli je jeste tarif platny
            $isValid = (bool)dibi::fetchSingle("
            SELECT (MONTH('$pli->numdatefrom')<=".$vals["month"]." AND YEAR('$pli->numdatefrom')=".$vals["year"].") OR
            YEAR('$pli->numdatefrom')<".$vals["year"]
            );
            if (!$isValid) continue;
          }
        }
        if (!empty($pli->numdateto)) {
          //zjistim jestli to je tento rok a mesic
          $dateToIsIn = (bool)dibi::fetchSingle("SELECT MONTH('$pli->numdateto')=".$vals["month"]." AND YEAR('$pli->numdateto')=".$vals["year"]);
          if (!$dateToIsIn) {
            //Zjistim jestli je jeste tarif platny
            $isValid = (bool)dibi::fetchSingle("
            SELECT (MONTH('$pli->numdateto')>=".$vals["month"]." AND YEAR('$pli->numdateto')=".$vals["year"].") OR
            YEAR('$pli->numdateto')>".$vals["year"]
            );
            if (!$isValid) continue;
          }
        }
        If ($dateFromIsIn && $dateToIsIn) {
          //obe data spadaji do uctovaciho obdobi
          $before = new \DateTime($pli->numdatefrom);
          $days = (int)$before->diff(new \DateTime($pli->numdateto))->format('%a days');
        } else If ($dateFromIsIn) {
          $before = new \DateTime($pli->numdatefrom);
          $days = (int)$before->diff(new \DateTime($vals["year"].'-'.$vals["month"].'-'.$monthDays))->format('%a');
        } else If ($dateToIsIn) {
          $before = new \DateTime($vals["year"].'-'.$vals["month"].'-01');
          $days = (int)$before->diff(new \DateTime($pli->numdateto))->format('%a');
        }
        if ($days < $monthDays) {
          //pocitam slevu z tarifu polde dni
          $plifeeFull = $plifee;
          $plifee = round(($plifeeFull * $days / $monthDays), 2);
          $disc = round(($plifeeFull - $plifee) * 100 / $plifeeFull, 0);
          $priTextAdd = " (za $days dní)";
        }

        $iVals = array(
          'nniserid' => 'o2',
          'nnimonth'=> $vals["month"],
          'nniyear'=> $vals["year"],
          'nninumber'=> $iniNumber,
          'nnivat'=> $this->config["INVOICE_DPHLEVEL_0"],
          'nnicnt'=> 1,
          'nniprice' => $plifee,
          'nnipricecnt' => $plifee,
          'nnitext' => 'Paušál pro číslo '.$iniNumber.$priTextAdd,
        );
        $numberitems->insert($iVals);
        $cnt++;

        //vlozim polozky
        if (!empty($subscriber->summaryData->usageCharges)) {
          foreach ($subscriber->summaryData->usageCharges->usageCharge as $usageCharge) {

            $data = $usageCharge->attributes();

            $text = (string)$data->usagePackName;

            $arr = [];

            $arr["nniserid"] = 'o2';
            $arr["nnimonth"] = $vals["month"];
            $arr["nniyear"] = $vals["year"];
            $arr["nnivat"] =  21;
            $arr["nninumber"] =  $iniNumber;
            $arr["nniprice"] = 0;
            $arr["nnicnt"] = 0;//$totalUnits;
            $arr["nnipricecnt"] = (int)$data->subtotalPrice; //cena bez DPH
            $arr["nnitext"] = $text . " (O2)";

            $id = $numberitems->insert($arr);
            if ($id == FALSE) {
              $this->flashMessage("Err: ".print_r($arr, true), 'err');
              $cntinsER++;
            }
          }
        }

        //vlozim polozky
        if (!empty($subscriber->summaryData->payments)) {

            $data = $subscriber->summaryData->payments->attributes();

            $text = 'služby 3. stran';

            $arr = [];

            $arr["nniserid"] = 'o2';
            $arr["nnimonth"] = $vals["month"];
            $arr["nniyear"] = $vals["year"];
            $arr["nnivat"] =  0;
            $arr["nninumber"] =  $iniNumber;
            $arr["nniprice"] = 0;
            $arr["nnicnt"] = 1;//$totalUnits;
            $arr["nnipricecnt"] = (int)$data->paymentTotalPrice; //cena bez DPH
            $arr["nnitext"] = $text . " (O2)";

            $id = $numberitems->insert($arr);
            if ($id == FALSE) {
              $this->flashMessage("Err: ".print_r($arr, true), 'err');
              $cntinsER++;
            }
        }

      }
      $this->flashMessage("Import dokončen: ".date('H:i:s')."(nové záznamy: $cnt bez chyb, $cntinsER chyb)");
      $this->redirect('default');
    }
  }

    protected function createComponentImportVodafoneInvItemsForm() {

    $form = new Nette\Application\UI\Form();
    $model = new \Model\CallsModel();
    $form->addUpload('imp_file', "Importní soubor (*.xlsx)", 100)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte importní soubor - XLSX formát!");

    $month = (int)date('m')-1;
    $year = (int)date('Y');
    if ($month == 0) {
      $month = 12;
      $year = $year - 1;
    }

    $form->addSelect('month', 'Měsíc', $model->getEnumMonId())
      ->setDefaultValue($month)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte měsíc");

    $form->addText('year', 'Rok', 4)
    ->setDefaultValue($year)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte rok");

    $form->addSubmit('check', 'Kontrolovat chybějící čísla');
    $form->addSubmit('save', 'Importovat');
    $form->onSuccess[] = array($this, 'importVodafoneInvItemsSubmitted');
    return $form;
  }

  public function importVodafoneInvItemsSubmitted (Nette\Application\UI\Form $form) {
    $usrs = new \Model\UsersModel();
    $fileName = "";
    $log = array();
    $numberitems = new \Model\NumberitemsModel();
    $plis = new \Model\PricelistsModel();

    $vals = $form->getValues();

    //zjistim jestli negeneruje data do budoucnosti
    $r = dibi::fetch("SELECT YEAR(CURDATE()) AS year, MONTH(CURDATE()) AS month");
    if (($r->year == $vals["year"] && $r->month <= $vals["month"]) || $r->year < $vals["year"]) {
      $err = true;
      $form->addError("Nemůžete importovat data pro aktuální nebo budoucí měsíce.(".$vals["month"]."/".$vals["year"].")");
      return;
    }

    if (!$vals["imp_file"][0]->isOk()) {
      $form->addError("Neplatný importní soubor.");
      return;
    } else {
      $fileName = $vals["imp_file"][0]->getTemporaryFile();
    }

    if ( $xlsx = SimpleXLSX::parse($fileName) ) {
      $rows = $xlsx->rows(0);
    } else {
      die(SimpleXLSX::parseError());
    }

    //z feedu načtu všechny tel čísla
    $phoneNumbers = [];
    $readNext = FALSE;
    foreach ($rows as $row) {
      $val = (string)$row[0];
      if ($val === 'Číslo služby') {
        $readNext = TRUE;
        continue;
      }
      if ($val === 'Celkem' && $readNext) {
        break;
      }
      if ($readNext) {
        $phoneNumbers[] = $val;
      }
    }

    //kontrola ceniků a čísel
    $errors = $this->checkPricelists($phoneNumbers, 'vodafone');

    //jen kontrola
    if ($form["check"]->isSubmittedBy()) {

      if (count($errors) === 0) {
        $this->flashMessage("Vše OK");
        $this->redirect("Import:default");
      } else {
        foreach ($errors as $error) {
          $form->addError($error);
        }
        return false;
      }

    } else if ($form["save"]->isSubmittedBy()) {

      $errors = [];

      if (count($errors) > 0) {
        foreach ($errors as $error) {
          $form->addError($error);
        }
        return false;
      }

      $this->flashMessage("Start: ".date('H:i:s'));

      //vymazu pokud tam neco uz je pro dane obdobi
      dibi::query("DELETE FROM numberitems WHERE nniserid='vodafone' AND nnimonth=%i", $vals["month"]," AND nniyear=%i", $vals["year"]);

      $cnt = 0;
      $cntinsER = 0;
      //$calls = new \Model\CallsModel();
      $arr = array();

      $vatOper = 1+($this->config["INVOICE_DPHLEVEL_0"]/100);

      $findNumber = true;
      $iniNumber = "";
      $monthDays = date('t', mktime(0, 0, 0, $vals["month"], 1, $vals["year"]));


      //PŘERCHROUSTÁM DATA DO xml
      $data = [];

      $rows = $xlsx->rows(2);
      $readNext = FALSE;
      $dataStart = FALSE;
      $number = NULL;
      foreach ($rows as $row) {
        $val = (string)$row[0];
        //$val = $this->convertToUtf8($val);
        if ($val === 'Jednotlivé služby') {
          $dataStart = TRUE;
        }

        if (!$dataStart) {
          continue;
        }

        if (substr($val, 0, 16) === 'Číslo služby:') {
          $number = trim(substr($val, 16));
          $readNext = TRUE;
          continue;
        }
        if ($val === 'Celkem za číslo služby') {
          $number = NULL;
          $readNext = FALSE;
        }

        if ($val === 'Služba/poplatek') {
          continue;
        }


        if ($number !== NULL) {
          if($readNext && trim($row[2]) !== 'Zvýhodněný tarif') {
            $dataRow["NUMBER"] = $number;
            $dataRow["NAME"] = (string)$row[0];
            $dataRow["NAME2"] = (string)$row[1];
            $dataRow["NAME3"] = (string)$row[2];
            $dataRow["COUNT"] = (string)$row[3];
            $dataRow["PRICE_VAT"] = (string)$row[11];
            $dataRow["PRICE_NO_VAT"] = (string)$row[8];
            $data[$number][] = $dataRow;
          }
        }
      }

      foreach ($data as $iniNumber => $dataRows) {

        //vlozim polozku za pausal pro dane cislo
        //zjistim cenik
        $pli = dibi::fetch("
          SELECT plifee, numusrid, numdatefrom, numdateto, plifreesms, plifreemms, plifreemin 
          FROM numbers 
              INNER JOIN pricelists ON (pliid=numpliid) 
          WHERE pliserid='vodafone' AND numnumber=%s", $iniNumber
        );

        $plifee = $pli->plifee;
        //vychazim z ceny bez DPH
        $plifee = (DOUBLE)round(($plifee / (1 + ($this->config["INVOICE_DPHLEVEL_0"]/100))), 2);
        $dateFromIsIn = False;
        $dateToIsIn = False;
        $days = $monthDays;
        $priTextAdd = "";

        //zjistim jestli mam slevnit tarif
        if (!empty($pli->numdatefrom)) {
          //zjistim jestli to je tento rok a mesic
          $dateFromIsIn = (bool)dibi::fetchSingle("SELECT MONTH('$pli->numdatefrom')=".$vals["month"]." AND YEAR('$pli->numdatefrom')=".$vals["year"]);
          if (!$dateFromIsIn) {
            //Zjistim jestli je jeste tarif platny
            $isValid = (bool)dibi::fetchSingle("
            SELECT (MONTH('$pli->numdatefrom')<=".$vals["month"]." AND YEAR('$pli->numdatefrom')=".$vals["year"].") OR
            YEAR('$pli->numdatefrom')<".$vals["year"]
            );
            if (!$isValid) continue;
          }
        }
        if (!empty($pli->numdateto)) {
          //zjistim jestli to je tento rok a mesic
          $dateToIsIn = (bool)dibi::fetchSingle("SELECT MONTH('$pli->numdateto')=".$vals["month"]." AND YEAR('$pli->numdateto')=".$vals["year"]);
          if (!$dateToIsIn) {
            //Zjistim jestli je jeste tarif platny
            $isValid = (bool)dibi::fetchSingle("
            SELECT (MONTH('$pli->numdateto')>=".$vals["month"]." AND YEAR('$pli->numdateto')=".$vals["year"].") OR
            YEAR('$pli->numdateto')>".$vals["year"]
            );
            if (!$isValid) continue;
          }
        }
        If ($dateFromIsIn && $dateToIsIn) {
          //obe data spadaji do uctovaciho obdobi
          $before = new \DateTime($pli->numdatefrom);
          $days = (int)$before->diff(new \DateTime($pli->numdateto))->format('%a days');
        } else If ($dateFromIsIn) {
          $before = new \DateTime($pli->numdatefrom);
          $days = (int)$before->diff(new \DateTime($vals["year"].'-'.$vals["month"].'-'.$monthDays))->format('%a');
        } else If ($dateToIsIn) {
          $before = new \DateTime($vals["year"].'-'.$vals["month"].'-01');
          $days = (int)$before->diff(new \DateTime($pli->numdateto))->format('%a');
        }
        if ($days < $monthDays) {
          //pocitam slevu z tarifu polde dni
          $plifeeFull = $plifee;
          $plifee = round(($plifeeFull * $days / $monthDays), 2);
          $disc = round(($plifeeFull - $plifee) * 100 / $plifeeFull, 0);
          $priTextAdd = " (za $days dní)";
        }

        $iVals = array(
          'nniserid' => 'vodafone',
          'nnimonth'=> $vals["month"],
          'nniyear'=> $vals["year"],
          'nninumber'=> $iniNumber,
          'nnivat'=> $this->config["INVOICE_DPHLEVEL_0"],
          'nnicnt'=> 1,
          'nniprice' => $plifee,
          'nnipricecnt' => $plifee,
          'nnitext' => 'Paušál pro číslo '.$iniNumber.$priTextAdd,
        );
        $numberitems->insert($iVals);
        $cnt++;

        //vlozim polozky
        foreach ($dataRows as $data) {

          $text = $data["NAME"] . " " . $data["NAME3"];

          $arr = [];

          $arr["nniserid"] = 'vodafone';
          $arr["nnimonth"] = $vals["month"];
          $arr["nniyear"] = $vals["year"];
          $arr["nnivat"] =  21;
          $arr["nninumber"] =  $iniNumber;
          $arr["nniprice"] = 0;
          $arr["nnicnt"] = 0;//$totalUnits;
          $arr["nnipricecnt"] = (float)$data["PRICE_NO_VAT"]; //cena bez DPH
          $arr["nnitext"] = $text . " (Vodafone)";

          $id = $numberitems->insert($arr);
          if ($id == FALSE) {
            $this->flashMessage("Err: ".print_r($arr, true), 'err');
            $cntinsER++;
          }
        }
      }
      $this->flashMessage("Import dokončen: ".date('H:i:s')."(nové záznamy: $cnt bez chyb, $cntinsER chyb)");
      $this->redirect('default');
    }
  }

  protected function createComponentImportTMobile2InvItemsForm() {

    $form = new Nette\Application\UI\Form();
    $model = new \Model\CallsModel();
    $form->addUpload('imp_file', "Importní soubor (*.csv)", 100)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte importní soubor - CSV formát!");

    $month = (int)date('m')-1;
    $year = (int)date('Y');
    if ($month == 0) {
      $month = 12;
      $year = $year - 1;
    }

    $form->addSelect('month', 'Měsíc', $model->getEnumMonId())
      ->setDefaultValue($month)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte měsíc");

    $form->addText('year', 'Rok', 4)
      ->setDefaultValue($year)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte rok");

    $form->addSubmit('check', 'Kontrolovat chybějící čísla');
    $form->addSubmit('save', 'Importovat');
    $form->onSuccess[] = array($this, 'importTMobile2InvItemsSubmitted');
    return $form;
  }

  public function importTMobile2InvItemsSubmitted (Nette\Application\UI\Form $form) {
    $usrs = new \Model\UsersModel();
    $fileName = "";
    $log = array();
    $numberitems = new \Model\NumberitemsModel();
    $plis = new \Model\PricelistsModel();

    $vals = $form->getValues();

    //zjistim jestli negeneruje data do budoucnosti
    $r = dibi::fetch("SELECT YEAR(CURDATE()) AS year, MONTH(CURDATE()) AS month");
    if (($r->year == $vals["year"] && $r->month <= $vals["month"]) || $r->year < $vals["year"]) {
      $err = true;
      $form->addError("Nemůžete importovat data pro aktuální nebo budoucí měsíce.(".$vals["month"]."/".$vals["year"].")");
      return false;
    }

    if (!$vals["imp_file"][0]->isOk()) {
      $form->addError("Neplatný importní soubor.");
      return false;
    } else {
      $fileName = $vals["imp_file"][0]->getTemporaryFile();
    }

    if ( $csv = SimpleCSV::parse($fileName, false, ";") ) {
    } else {
      die("Soubor se nepodařilo načíst");
    }

    //z feedu načtu všechny tel čísla
    $phoneNumbers = [];
    $cnt = 0;
    foreach ($csv->rows() as $row) {
      $cnt++;
      if ($cnt === 1) {
        continue;
      }

      if (empty($row[0])) {
        continue;
      }

      if (substr($row[0], 0, 3) === "INT") {
        continue;
      }

      $phoneNumbers[] = '420' . $row[0];

    }
    //kontrola ceniků a čísel
    $errors = $this->checkPricelists($phoneNumbers, 'tmobile2');

    //jen kontrola
    if ($form["check"]->isSubmittedBy()) {

      if (count($errors) === 0) {
        $this->flashMessage("Vše OK");
        $this->redirect("Import:default");
      } else {
        foreach ($errors as $error) {
          $form->addError($error);
        }
        return false;
      }

    } else if ($form["save"]->isSubmittedBy()) {

      $errors = [];

      if (count($errors) > 0) {
        foreach ($errors as $error) {
          $form->addError($error);
        }
        return false;
      }

      $this->flashMessage("Start: ".date('H:i:s'));

      //vymazu pokud tam neco uz je pro dane obdobi
      dibi::query("DELETE FROM numberitems WHERE nniserid='tmobile2' AND nnimonth=%i", $vals["month"]," AND nniyear=%i", $vals["year"]);

      $cntinsER = 0;
      $monthDays = date('t', mktime(0, 0, 0, $vals["month"], 1, $vals["year"]));

      //PŘERCHROUSTÁM DATA
      $data = [];

      $cnt = 0;
      foreach ($csv->rows() as $row) {
        $cnt++;
        if ($cnt === 1) {
          continue;
        }
          $row[6] = $this->convertToUtf8((string)$row[6]);

        if (trim($row[6]) === 'Měsíční paušál') {
          continue;
        }

        if (trim($row[6]) === 'Součet') {
          continue;
        }

        if (empty($row[0])) {
            break;
        }

        $number = '420' . $row[0];

        if (!empty($number)) {
            $dataRow["NUMBER"] = $number;
            $dataRow["NAME"] = $row[6];
            $dataRow["COUNT"] = (string)$row[8];
            $dataRow["UNIT"] = (string)$row[9];
            $dataRow["VAT"] = (string)$row[13];
            $dataRow["PRICE_NO_VAT"] = (float)$this->formatNumber($row[10]);
            $data[$number][] = $dataRow;
        }

        $cnt++;
      }

      foreach ($data as $iniNumber => $dataRows) {

        //vlozim polozku za pausal pro dane cislo
        //zjistim cenik
        $pli = dibi::fetch("
          SELECT plifee, numusrid, numdatefrom, numdateto, plifreesms, plifreemms, plifreemin 
          FROM numbers 
              INNER JOIN pricelists ON (pliid=numpliid) 
          WHERE pliserid='tmobile2' AND numnumber=%s", $iniNumber
        );

        $plifee = $pli->plifee;
        //vychazim z ceny bez DPH
        $plifee = (DOUBLE)round(($plifee / (1 + ($this->config["INVOICE_DPHLEVEL_0"]/100))), 2);
        $dateFromIsIn = False;
        $dateToIsIn = False;
        $days = $monthDays;
        $priTextAdd = "";

        //zjistim jestli mam slevnit tarif
        if (!empty($pli->numdatefrom)) {
          //zjistim jestli to je tento rok a mesic
          $dateFromIsIn = (bool)dibi::fetchSingle("SELECT MONTH('$pli->numdatefrom')=".$vals["month"]." AND YEAR('$pli->numdatefrom')=".$vals["year"]);
          if (!$dateFromIsIn) {
            //Zjistim jestli je jeste tarif platny
            $isValid = (bool)dibi::fetchSingle("
            SELECT (MONTH('$pli->numdatefrom')<=".$vals["month"]." AND YEAR('$pli->numdatefrom')=".$vals["year"].") OR
            YEAR('$pli->numdatefrom')<".$vals["year"]
            );
            if (!$isValid) continue;
          }
        }
        if (!empty($pli->numdateto)) {
          //zjistim jestli to je tento rok a mesic
          $dateToIsIn = (bool)dibi::fetchSingle("SELECT MONTH('$pli->numdateto')=".$vals["month"]." AND YEAR('$pli->numdateto')=".$vals["year"]);
          if (!$dateToIsIn) {
            //Zjistim jestli je jeste tarif platny
            $isValid = (bool)dibi::fetchSingle("
            SELECT (MONTH('$pli->numdateto')>=".$vals["month"]." AND YEAR('$pli->numdateto')=".$vals["year"].") OR
            YEAR('$pli->numdateto')>".$vals["year"]
            );
            if (!$isValid) continue;
          }
        }
        If ($dateFromIsIn && $dateToIsIn) {
          //obe data spadaji do uctovaciho obdobi
          $before = new \DateTime($pli->numdatefrom);
          $days = (int)$before->diff(new \DateTime($pli->numdateto))->format('%a days');
        } else If ($dateFromIsIn) {
          $before = new \DateTime($pli->numdatefrom);
          $days = (int)$before->diff(new \DateTime($vals["year"].'-'.$vals["month"].'-'.$monthDays))->format('%a');
        } else If ($dateToIsIn) {
          $before = new \DateTime($vals["year"].'-'.$vals["month"].'-01');
          $days = (int)$before->diff(new \DateTime($pli->numdateto))->format('%a');
        }
        if ($days < $monthDays) {
          //pocitam slevu z tarifu polde dni
          $plifeeFull = $plifee;
          $plifee = round(($plifeeFull * $days / $monthDays), 2);
          $disc = round(($plifeeFull - $plifee) * 100 / $plifeeFull, 0);
          $priTextAdd = " (za $days dní)";
        }

        $iVals = array(
          'nniserid' => 'tmobile2',
          'nnimonth'=> $vals["month"],
          'nniyear'=> $vals["year"],
          'nninumber'=> $iniNumber,
          'nnivat'=> $this->config["INVOICE_DPHLEVEL_0"],
          'nnicnt'=> 1,
          'nniprice' => $plifee,
          'nnipricecnt' => $plifee,
          'nnitext' => 'Paušál pro číslo '.$iniNumber.$priTextAdd,
        );
        $numberitems->insert($iVals);
        $cnt++;

        //vlozim polozky
        foreach ($dataRows as $data) {

          $text = $data["NAME"];

          $arr = [];

          $arr["nniserid"] = 'tmobile2';
          $arr["nnimonth"] = $vals["month"];
          $arr["nniyear"] = $vals["year"];
          $arr["nnivat"] =  (int)$data["VAT"];
          $arr["nninumber"] =  $iniNumber;
          $arr["nniprice"] = 0;
          $arr["nnicnt"] = $data["COUNT"];//počet účtovaných jednotek
          $arr["nniunit"] = $data["UNIT"];//$jednotka;
          $arr["nnipricecnt"] = $data["PRICE_NO_VAT"]; //cena bez DPH
          $arr["nnitext"] = $text . " (T-Mobile)";

          $id = $numberitems->insert($arr);
          if ($id == FALSE) {
            $this->flashMessage("Err: ".print_r($arr, true), 'err');
            $cntinsER++;
          }
        }
      }
      $this->flashMessage("Import dokončen: ".date('H:i:s')."(nové záznamy: $cnt bez chyb, $cntinsER chyb)");
      $this->redirect('default');
    }
  }

  private function checkPriceLists($phoneNumbers, $serId) {
    $plis = new PricelistsModel();
    $usrs = new UsersModel();

    $formError = [];

    //nactu pouzite ceniky
    $rows = dibi::fetchAll("SELECT numpliid, numnumber FROM numbers INNER JOIN users ON (usrid=numusrid) WHERE usrstatus IN (0,2) GROUP BY numpliid");
    foreach ($rows as $row) {
      $pli = $plis->load($row->numpliid);
      if ($pli) {
        if ($pli->plistatus == 1) {
          $formError[] = "U čísla ".$row->numnumber." je pouzitý ceník ID=".$row->numpliid." který je blokovaný";
        }
      } else {
        $formError[] = "Ceník ID=".$row->numpliid." neexistuje nebo není u čísla ".$row->numnumber." vyplněný";
      }
    }

    foreach ($phoneNumbers as $iniNumber) {

      //zjistim cenik
      $pli = dibi::fetch("
          SELECT plifee, numusrid, numdatefrom, numdateto, plifreesms, plifreemms, plifreemin 
          FROM numbers 
              INNER JOIN pricelists ON (pliid=numpliid) 
          WHERE pliserid=%s",  $serId,  " AND numnumber=%s", $iniNumber
      );

      if (!$pli) {
        $formError[] = "Telefonní číslo $iniNumber nebylo nalezeno v databázi, nebo nemá přiřazený ceník pro $serId. Doplňte jej a proveďte znovu import.";
      } else {
        $usr = $usrs->load($pli->numusrid);
        if ($usr->usrstatus == 1) {
          $formError[] = "Zákazník $usr->usrname s číslem $iniNumber je blokovaný.";
        }
      }
    }

    return $formError;
  }

  private function convertToUtf8($value) {
    return iconv("WINDOWS-1250", "UTF-8", $value);
  }

}