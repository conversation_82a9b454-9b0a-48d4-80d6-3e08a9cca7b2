<?php
namespace AdminModule;
use dibi;
use Nette;

final class PricelistPresenter extends BasePresenter {

  public $backlink = '';

  /** @persistent */
  public $serid;
  
  public function userEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $pricelists = new \Model\PricelistsModel();
      $id = $this->getParam('id');
      $vals = $form->getValues();
      //prevedu cisla
      $vals->plifee = $this->formatNumber($vals->plifee);
      /*
      $vals->plipricesms = $this->formatNumber($vals->plipricesms);
      $vals->plipricesmsfor = $this->formatNumber($vals->plipricesmsfor);
      $vals->plipricevnitro = $this->formatNumber($vals->plipricevnitro);
      $vals->plipricemobil = $this->formatNumber($vals->plipricemobil);
      $vals->plipricemobilfor = $this->formatNumber($vals->plipricemobilfor);
      $vals->plipricepevna = $this->formatNumber($vals->plipricepevna);
      $vals->plipricepevnafor = $this->formatNumber($vals->plipricepevnafor);
      $vals->plidata1price = $this->formatNumber($vals->plidata1price);
      $vals->plidata2price = $this->formatNumber($vals->plidata2price);
      $vals->plidata3price = $this->formatNumber($vals->plidata3price);
      $vals->plidata4price = $this->formatNumber($vals->plidata4price);
      $vals->plidata5price = $this->formatNumber($vals->plidata5price);
      $vals->plidata6price = $this->formatNumber($vals->plidata6price);
      */
      if ($id > 0) {
        $pricelists->update($id, $vals);  
        $this->flashMessage('Aktualizováno v pořádku');
      } else {
        $pricelists->insert($vals);  
        $this->flashMessage('Nový záznam uložen v pořádku');
      }
    }
    $this->redirect('default');
  } 
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    $pricelists = new \Model\PricelistsModel();
    $h = (int)$this->getParameter("h");
    $where = " WHERE plistatus=".($h==1 ? 1 : 0);

    if (!empty($this->serid)) {
      $where .= " AND pliserid='" . $this->serid . "'";
    }

    $dataRows = $pricelists->fetchAll("SELECT * FROM pricelists $where ORDER BY pliname");
    $this->template->dataRows = $dataRows;           
    
    //ciselnik statusu
    $this->template->enum_plistatus = $pricelists->getEnumPliStatus();
  }
  
  public function renderEdit($id) {
    $form = $this['pricelistEditForm'];
    
    if (!$form->isSubmitted()) {
      $pricelists = new \Model\PricelistsModel();
      if ($id >0) {
        $dataRow = $pricelists->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
        $this->template->dataRow = $dataRow; 
      }
    }
  }
  
  public function renderDelete($id) {
    if ($id > 0) {
      $pricelists = new \Model\PricelistsModel();

      $numnumber = (int)dibi::fetchSingle("SELECT numnumber FROM numbers WHERE CURDATE() between COALESCE(numdatefrom,CURDATE()) AND COALESCE(numdateto, CURDATE()) AND numpliid=%i", $id);
      if ($numnumber > 0) {
        $this->flashMessage('Záznam nebylo možno vymazat, ceník je použit u čísla ' . $numnumber, "err");
      } else {
        $pricelists->delete($id);
        $this->flashMessage('Záznam byl vymazán');
      }
    }
    $this->redirect('default');           
  }
  
  
  /********************* facilities *********************/

  protected function createComponentPricelistEditForm() {
    $pricelists = new \Model\PricelistsModel();
    $form = new Nette\Application\UI\Form();
    $form->addGroup();
    $form->addText('pliname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název.');

    $form->addSelect("pliserid", "Poskytovatel:", $pricelists->getEnumSerId())
      ->setPrompt("Vyberte ...")
      ->setDefaultValue($this->serid)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addText('plifee', 'Měsíční paušál:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte měsíční paušál.');
    
    $form->addText('plifreemin', 'Volné minuty:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label');  
    
    $form->addText('plifreesms', 'Volné SMS:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label');

    $form->addText('plifreemms', 'Volné MMS:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label');
      
    /*  
    $form->addText('plipricesms', 'Cena SMS místní síť [za kus]:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label');  
    $form->addText('plipricesmsfor', 'Cena SMS do cizí sítě [za kus]:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label');  
    $form->addText('plipricevnitro', 'Cena vnitrofiremní volání [za min.]:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label');
    $form->addText('plipricemobil', 'Cena volání do místní mobilní sítě [za min.]:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label');  
    $form->addText('plipricemobilfor', 'Cena volání ostaních mobilních sítí [za min.]:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label');          
    $form->addText('plipricepevna', 'Cena volání na pevnou linku do místní sítě [za min.]:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label');          
    $form->addText('plipricepevnafor', 'Cena volání na pevnou linku u cizí sítě [za min.]:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label');          
    
    $arr = $pricelists->getEnumPliDataName();
    $form->addGroup("Datové tarify");
    foreach ($arr as $key => $text) {
      $form->addText("plidata".$key."price", "$text:", 10);  
    }
    $form->addGroup();
    */
    $form->addTextArea('plitext', 'Ceny textově:', 40, 15)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label');
    
    $form->addCheckbox("pliprivate", "VIP ceník");
    $form->addCheckbox("plistatus", "skrýt ceník");
      
    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'userEditFormSubmitted');

    return $form;  
  } 
}  
?>
