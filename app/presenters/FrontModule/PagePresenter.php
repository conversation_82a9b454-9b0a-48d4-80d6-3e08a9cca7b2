<?php
namespace FrontModule;
use dibi;
use Nette;

class PagePresenter extends BasePresenter {
  
  public function renderDetail($key) {
    $pages = new \Model\PagesModel();
    $pageData = $pages->load($key, 'urlkey');
    if ($pageData) {
      if ($pageData->pagid > 0) {
        $this->template->urlkey = $pageData->pagurlkey;
        $this->template->page = $pageData;
      }
    } else {
      throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    }
    
    //pokud nejaky specialni typ doplnim potrebna data nastavim sablonu
    switch ($pageData->pagtypid) {
      case 1:  
        //kontaktni form
        $this->setView('contact');
        break;       
    }
       
  }
  
  public function contactFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVals = $form->getValues();
      unset($formVals["antispam"]);
        
      $body = 'Nový dotaz, vzkaz:<br />
      <PERSON><PERSON><PERSON>, Přijmení: '.$formVals["conname"].'<br />
      Email: '.$formVals["conmail"].'<br />
      Mobil: '.$formVals["congsm"].'<br />
      Poznámka: <br />
      '.nl2br($formVals["connote"]);
      $mail = new Nette\Mail\Message();
      $mail->setFrom($this->config["SERVER_NAME"].' <'.$this->config["SERVER_MAIL"].'>');
      $mail->addReplyTo($formVals["conmail"]);
      $mail->addTo($this->config["SERVER_MAIL"]);
      $mail->setSubject('Nový dotaz, vzkaz');
      $mail->setHtmlBody($body);  
      try {
        $this->mailer->send($mail);
        $this->flashMessage("Váš vzkaz byl přijat. Děkujeme!");
        $this->redirect('this');
      } catch (InvalidStateException $e) {
        $someerr = true;
        $form->addError("Vzkaz se nepodařilo odeslat.");
      }
    }
  }   

  protected function createComponentContactForm() {
    $form = $this->createAppForm();
    
    $form->addText('conname', 'Jméno, příjmení', 30);
    
    $form->addText('conmail', "Platná emailová adresa (nutno vyplnit)", 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte platný email')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát.');
    
    $form->addText('congsm', 'Telefon (mobil) pro rychlejší kontakt', 20);
    
    $form->addTextArea('connote', "Poznámka, dotaz či přání", 50, 8);
    
    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"],  10)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte antispamové číslo')
      ->setOption('description', 'test proti robotum')
      ->setHtmlId('antispam')
      ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotům', $this->config["ANTISPAM_NO"]);
    
    //$form->setRenderer(new ScaffoldingRenderer); 
      
    $form->addSubmit('save', 'Odeslat')->getControlPrototype()->class('button');  
    $form->onSuccess[] = array($this, 'contactFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
      
  }
}  
?>
