<?php
namespace FrontModule;
use dibi;
use Nette;

class ScriptPresenter extends BasePresenter {

	public function actionPairPayments() {
    $mailLog = array();
    $invs = new \Model\InvoicesModel();
    $k = (string)$this->getParameter('k');
    if ($k != '942a13a8cb5840') return;
    $d = (int)$this->getParameter('d');
    $accId = (int)$this->getParameter('a');
    if ($d == 0) $d = 2;
    $mailLog[] = date('d.m.Y H:i:s').":Párování začalo.(výpis $d dny zpětně)";
    //nactu neuzhrazene dokumenty
    $notPayed = dibi::query("
      SELECT invoices.*
      FROM invoices 
      INNER JOIN users ON (invusrid=usrid) 
      WHERE invstatus=0 AND 
      (invcode IS NOT NULL OR (invcode IS NULL AND usrpayrateid=0)) 
      ORDER BY invpaydate
    ")->fetchAssoc("invvarsym");
    $mailLog[] = date('d.m.Y H:i:s').":".count($notPayed)." neuhrazených faktur.";
    $data = array();
    if ($accId == 0) {
      //nactu vypis hlavni ucet
      $params = $this->neonParameters["fio"];
      $fio = new \FioApi($params);
      $data = $fio->getMovements('-' . $d . ' day');
	  echo "OK: Vypis z hlavniho ucet nacten.";
    } else {
      //nactu vypis druhy ucet
      $params = $this->neonParameters["fio2"];
      $fio = new \FioApi($params);
      $data = $fio->getMovements('-'.$d.' day');
	  echo "OK: Vypis druhy ucet nacten.";
      /*
      foreach ($data2 as $item) {
        $data[] = $item;
      }
      */
    }
    $cnt = 0;
    if (empty($data)) {
		echo "ERR: Výpis z banky prázdný.";
		$this->terminate();
    }
    foreach ($data as $key => $row) {
      if (empty($row["variableSymbol"])) continue;
      $varsym = (int)$row["variableSymbol"]; 
      if (isset($notPayed[$varsym])) {
        $invoice = $notPayed[$varsym];
        $cnt ++; 
        //nasel sem uhradu - kontroluju cenu
        if ($row["amount"] == $invoice->invpricevat) {
          //oznacim jako zaplacene
          //nasel jsem uhradu - nastavim fa jako uhrazenou  
          $vals = array(
            'invstatus'=> 1,
            'invpayeddate'=> substr($row["moveDate"], 0, 10),
          );  
          $ret = false;
          $ret = $invs->update($invoice->invid, $vals);
          if ($ret) {
            
            $mailLog[] = date('d.m.Y H:i:s').":OK: Faktura ".$invoice->invcode." (vs: ".$invoice->invvarsym.") spárovaná a nastavena jako uhrazena.";
            //mailuju
            if (!empty($invoice->invmail)) {
              $template = $this->getTemplate();
              $template->setFile(Nette\Environment::expand('%wwwDir%/../templates/mailZaplaceno.phtml'));
              $template->invoice = $invoice;
              $this->sendMail($invoice->invmail, "Úhrada přijata", $template);
            }
          } else {  
            $mailLog[] = date('d.m.Y H:i:s').":ERR: Faktura ".$invoice->invcode." (vs: ".$invoice->invvarsym.") nepodarilo se nastavit priznak zaplaceno.".print_r($vals, true);
          } 
        } else {
          $mailLog[] = date('d.m.Y H:i:s').":INFO: Faktura ".$invoice->invcode." (vs: ".$invoice->invvarsym.") stejný variabilní symbol ve výpisu, ale jiná částka (výpis: ".$row["amount"].", fa: ".$invoice->invpricevat.").";
        }
      }    
    }
    $mailLog[] = date('d.m.Y H:i:s').":Párování dokončeno";
    if ($cnt > 0) {
      $this->sendMail($this->config["SERVER_MAIL"], 'telcall.cz: parovani plateb', implode('<br>', $mailLog));
    }
    //$this->sendMail('<EMAIL>', 'telcall.cz: parovani plateb', implode('<br>', $mailLog));

    $this->terminate();
  }
  
  public function actionBackup() {
    $k = (string)$this->getParam('k');
    if ($k != '942a13a8cb5840') return;
    $info = dibi::getConnection()->getConfig();
    $dw = date("w");
    $m = date("n");
    $backupFileDay = WWW_DIR.'/../log/backup/day-'.$dw.'-backup.sql.gz';
    $backupFileMonth = WWW_DIR.'/../log/backup/month-'.$m.'-backup.sql.gz';
    require_once(LIBS_DIR.'/MySQLDump/MySQLDump.php');
    $dump = new \MySQLDump(new \mysqli($info["host"], $info["username"], (string)$info["password"], (string)$info["database"]));
    $dump->save($backupFileDay);
    copy($backupFileDay, $backupFileMonth);
    $this->terminate();
  }
  
  public function actionMailDebetors() {
    $k = (string)$this->getParam('k');
    if ($k != '942a13a8cb5840') return;
    
    $notPayedAfter = dibi::fetchAll("
    SELECT *, 
    DATEDIFF(CURDATE(),invpaydate) AS invduedateafter 
    FROM invoices 
    INNER JOIN users ON (invusrid=usrid) 
    WHERE invstatus=0 AND 
    (invcode IS NOT NULL OR (invcode IS NULL AND usrpayrateid=0)) AND 
    (DATEDIFF(CURDATE(),invpaydate) = 2 OR DATEDIFF(CURDATE(),invpaydate) = 10 OR DATEDIFF(CURDATE(),invpaydate) >= 15) 
    ORDER BY invpaydate
    ");
    
    $debtors15 = array();
    $debtors30 = array();
    foreach ($notPayedAfter as $row) {  
      If ($row->invduedateafter == 2 || $row->invduedateafter == 10) {
        $template = $this->getTemplate();
        //1. a 2. vyzva
        $template->invoice = $row;
        $subject = ($row->invduedateafter == 2 ? '1. upomínka' : '2. upomínka')." úhrada";
        
        if (empty($row->invmail) || $row->usrsendtypid == 2 || $row->invduedateafter > 2) {
          $template->setFile(Nette\Environment::expand('%wwwDir%/../templates/smsReminderUser.phtml'));
          //poslu SMS
          $this->sendSms($row->usrphone, $template);
        }
        if (!empty($row->invmail)) {
          $template->setFile(Nette\Environment::expand('%wwwDir%/../templates/mailReminderUser.phtml'));
          $this->sendMail($row->invmail, $subject, $template);    
        }
      } else If ($row->invduedateafter >= 30 && $row->usrstatus != 2) {   
        //ulozim neplatice, vse se pak posle v jednom mailu s zadosti o odpojeni cisla
        $debtors30[$row->usrid] = $row;  
      } else If ($row->invduedateafter >= 15 && $row->usrstatus != 2) {   
        //ulozim neplatice, vse se pak posle v jednom mailu s zadosti o odpojeni cisla - jen ty co uz nedluzi vice jak 30
        if (!array_key_exists($row->usrid, $debtors30)) $debtors15[$row->usrid] = $row;
      }
    }
    //mailuju dluzniky spravci
    if (count($debtors15) > 0 || count($debtors30) > 0) {
      $template = $this->getTemplate();
      $usrs = new \Model\UsersModel();
      //doplnim cisla k dluznikum
      foreach ($debtors15 as $key => $row) {
        $debtors15[$key]["numbers"] = dibi::fetchAll("SELECT * FROM numbers WHERE numusrid=%i", $row->usrid);
        //nastavim status dluznik
        $usrs->update($row->usrid, array('usrstatus'=>2));
        if (count($debtors15[$key]["numbers"]) == 0) unset($debtors15[$key]);
      }
      foreach ($debtors30 as $key => $row) {
        $debtors30[$key]["numbers"] = dibi::fetchAll("SELECT * FROM numbers WHERE numusrid=%i", $row->usrid);  
        $usrs->update($row->usrid, array('usrstatus'=>2));
        if (count($debtors30[$key]["numbers"]) == 0) unset($debtors30[$key]);
      }
      $template->debtors15 = $debtors15;
      $template->debtors30 = $debtors30;
      
      if (count($debtors15) > 0 || count($debtors30) > 0) {
        //mailuju adminovi
        $template->setFile(Nette\Environment::expand('%wwwDir%/../templates/mailReminderAdmin.phtml'));
        $this->sendMail($this->config["SERVER_MAIL"], 'telcall.cz: Žádost o blokaci cisel', $template);  
        //$this->sendMail('<EMAIL>', 'telcall.cz: Žádost o blokaci čísel', $template);  
      }  
    }
    $this->terminate();
  }
}
