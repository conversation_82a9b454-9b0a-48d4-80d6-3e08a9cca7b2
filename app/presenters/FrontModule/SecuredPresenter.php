<?php
namespace FrontModule;
use dibi;
use Nette;

abstract class SecuredPresenter extends BasePresenter {
	protected function startup() {
		parent::startup();
    
    if ($this->user->isLoggedIn() && ($this->action == "logIn" || $this->action == "add" || $this->action == "newPassword")) {
      $this->redirect('User:edit');
    } else {  
      if ($this->name == "Front:User" && ($this->action == "logIn" || $this->action == "add" || $this->action == "newPassword")) {
        //neni treba autorizace
      } else {
        //je vyzadovano prihlaseni 
        if (!$this->user->isLoggedIn()) {
          $this->appSession->backlink = $this->storeRequest();
          if ($this->user->getLogoutReason() === \Nette\Security\IUserStorage::INACTIVITY) {
            $this->flashMessage('Byl/a jste odhl<PERSON>en/a z důvodu delší neaktivity.');
          }
          $this->redirect('User:logIn');
        }
      }
    }
	}
}