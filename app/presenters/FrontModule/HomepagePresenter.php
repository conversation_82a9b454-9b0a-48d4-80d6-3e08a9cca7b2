<?php
namespace FrontModule;
use dibi;
use Nette;

class HomepagePresenter extends BasePresenter {

	public function renderDefault() {
    //neuhrazene faktury
    
    $this->template->invoicesNotPayed = dibi::fetchAll("
      SELECT invoices.*, DATEDIFF(CURDATE(),invpaydate) AS invduedateafter,usrpayrateid 
      FROM invoices 
      INNER JOIN users ON (usrid=invusrid)
      WHERE 
      invstatus=0 AND 
      (invcode IS NOT NULL OR (invcode IS NULL AND usrpayrateid=0)) AND 
      invusrid=%i", $this->userData->usrid);  
	}
  
  protected function createComponentChangeTarifForm() {
    $form = new Nette\Application\UI\Form;
    $form->addHidden('usrid', $this->userData->usrid);

    $plis = new \Model\PricelistsModel();
    $form->addSelect('pliid', 'Nový ceník:', $plis->getEnumPliId("pliprivate=0"))
      ->setPrompt("Vyberte ... ")
      ->setRequired('Zadejte %label.');

    $numbers = dibi::query("SELECT numnumber FROM numbers WHERE numusrid=%i", $this->userData->usrid)->fetchPairs("numnumber", "numnumber");  
    $form->addSelect('numnumber', 'Pro telefonní číslo:', $numbers)
      ->setPrompt("Vyberte ... ")
      ->setRequired('Zadejte %label.');
  
    $form->addSubmit('submit', 'Požádat o změnu ceníku');

    $form->onSuccess[] = callback($this, 'changeTarifFormSubmitted');
    return $form;
  }
  
  public function changeTarifFormSubmitted(Nette\Application\UI\Form $form) {
    $values = $form->getValues();
    $plis = new \Model\PricelistsModel();
    
    //ulozim do db
    $tacs = new \Model\TarifchangesModel();
    $tacs->insert(array(
      'tacpliid'=>$values->pliid,
      'tacusrid'=>$this->userData->usrid,
      'tacnumber'=>$values->numnumber,
    ));
    
    $template = $this->createTemplate();
    $template->priceList = $plis->load($values->pliid);

    //service email podle ceníku
    $operatorMail = "";
    if (isset($this->configOperators[$template->priceList->pliserid])) {
      $operatorMail = $this->configOperators[$template->priceList->pliserid]["email"];
    }

    $template->user = $this->userData;
    $template->phoneNumber = $values->numnumber;
    $template->setFile(WWW_DIR . '/../templates/mailNewTarif.phtml');
    try {
      $this->sendMail($this->config["SERVER_MAIL"], 'Telcall - požadavek na změnu tarifu', $template);
      $this->sendMail('<EMAIL>', 'Telcall - požadavek na změnu tarifu', $template);
      if ($operatorMail !== "") {
        $this->sendMail($operatorMail, 'Telcall - požadavek na změnu tarifu', $template);
      }
      $this->flashMessage("Děkujeme Váš požadavek byl odeslán. O změně Vás budeme informovat.");
      $this->redirect("default");
    } catch (Nette\Security\AuthenticationException $e) {
      $form->addError("Odeslání požadavku se nezdařilo.");
    }
  }
}
