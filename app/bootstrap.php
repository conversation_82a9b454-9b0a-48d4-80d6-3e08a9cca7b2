<?php
use Nette\Application\Routers\Route;

// Load Nette Framework
require LIBS_DIR . '/Nette/loader.php';


// Configure application
$configurator = new Nette\Config\Configurator;

// Enable Nette Debugger for error visualisation & logging
//$configurator->setDebugMode($configurator::AUTO);
$configurator->setDebugMode(array('31.30.172.149','172.29.0.1','::1'));
//$configurator->setDebugMode(array('86.49.166.19','127.0.0.1','::1'));
$configurator->enableDebugger(__DIR__ . '/../log', '<EMAIL>');

// Enable RobotLoader - this will load all classes automatically
$configurator->setTempDirectory(__DIR__ . '/../temp');
$configurator->createRobotLoader()
  ->addDirectory(APP_DIR)
  ->addDirectory(LIBS_DIR)
  ->register();

// Create Dependency Injection container from config.neon file
$configurator->addConfig(__DIR__ . '/config/config.neon', Nette\Config\Configurator::AUTO);
$container = $configurator->createContainer();

// dibi
//dibi::connect($container->params['database']);
dibi::setConnection($container->getService("connection"));

Route::$defaultFlags = Route::SECURED;

//Setup application router
$router = new Nette\Application\Routers\RouteList;

$router[] = new Route('index.php', 'Front:Homepage:default', Route::ONE_WAY);
$router[] = new Route('administrace/<presenter>/<action>/[<id>]', array(
    'module' => 'Admin',
    'presenter' => 'Admin',
    'action' => 'default',
));

$router[] = new Route('kategorie-<key>/', array(
    'module' => 'Front',
    'presenter' => 'Catalog',
    'action' => 'category',
    'key' => NULL,
));

$router[] = new Route('kategorie-<key>/<key2>/', array(
    'module' => 'Front',
    'presenter' => 'Catalog',
    'action' => 'subCategory',
    'key' => NULL,
    'key2' => NULL
));

$router[] = new Route('diskuze-<key>/<id [0-9]+>/', array(
    'module' => 'Front',
    'presenter' => 'Forum',
    'action' => 'detail',
    'key' => NULL
));

$router[] = new Route('text-<key>', array(
    'module' => 'Front',
    'presenter' => 'Page',
    'action' => 'detail',
    'key' => NULL
));

$router[] = new Route('<presenter>/<action>/<id>', array(
    'module' => 'Front',
    'presenter' => 'Homepage',
    'action' => 'default',
    'id' => NULL,
));

$container->addService('router', $router);

// Configure and run the application!
$container->getService("application")->run();