/**
 * This file is part of the Tracy (https://tracy.nette.org)
 */

/* common styles */
#tracy-debug {
	display: none;
	direction: ltr;
}

body#tracy-debug {
	margin: 5px 5px 0;
	display: block;
}

body #tracy-debug {
	position: absolute;
	bottom: 0;
	right: 0;
}

#tracy-debug * {
	font: inherit;
	color: inherit;
	background: transparent;
	margin: 0;
	padding: 0;
	border: none;
	text-align: inherit;
	list-style: inherit;
	opacity: 1;
	border-radius: 0;
	box-shadow: none;
	text-shadow: none;
}

#tracy-debug *:before
#tracy-debug *:after {
	all: unset;
}

#tracy-debug b,
#tracy-debug strong {
	font-weight: bold;
}

#tracy-debug i,
#tracy-debug em {
	font-style: italic;
}

#tracy-debug a {
	color: #125EAE;
	text-decoration: none;
}

#tracy-debug .tracy-panel a {
	color: #125EAE;
	text-decoration: none;
}

#tracy-debug a:hover,
#tracy-debug a:active,
#tracy-debug a:focus {
	background-color: #125EAE;
	color: white;
}

#tracy-debug .tracy-panel h2,
#tracy-debug .tracy-panel h3,
#tracy-debug .tracy-panel p {
	margin: .4em 0;
}

#tracy-debug .tracy-panel table {
	border-collapse: collapse;
	background: #FDF5CE;
}

#tracy-debug .tracy-panel tr:nth-child(2n) td {
	background: #F7F0CB;
}

#tracy-debug .tracy-panel td,
#tracy-debug .tracy-panel th {
	border: 1px solid #E6DFBF;
	padding: 2px 5px;
	vertical-align: top;
	text-align: left;
}

#tracy-debug .tracy-panel th {
	background: #F4F3F1;
	color: #655E5E;
	font-size: 90%;
	font-weight: bold;
}

#tracy-debug .tracy-panel pre,
#tracy-debug .tracy-panel code {
	font: 9pt/1.5 Consolas, monospace;
}

#tracy-debug table .tracy-right {
	text-align: right;
}

#tracy-debug svg {
	display: inline;
}


/* bar */
#tracy-debug-bar {
	font: normal normal 13px/1.55 Tahoma, sans-serif;
	color: #333;
	border: 1px solid #c9c9c9;
	background: #EDEAE0 url('data:image/png;base64,R0lGODlhAQAUALMAAOzq4e/t5e7s4/Dt5vDu5e3r4vDu5uvp4O/t5AAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAAAAAAALAAAAAABABQAAAQM0EgySEAYi1LA+UcEADs=') top;
	background-size: 1em;
	position: fixed;
	right: 0;
	bottom: 0;

	min-width: 50px;
	white-space: nowrap;

	z-index: 30000;
	opacity: .9;
	transition: opacity 0.2s;

	border-radius: 3px;
	box-shadow: 1px 1px 10px rgba(0, 0, 0, .15);
}

#tracy-debug-bar:hover {
	opacity: 1;
	transition: opacity 0.1s;
}

#tracy-debug-bar ul {
	list-style: none none;
	clear: left;
}

#tracy-debug-bar li {
	float: left;
}

#tracy-debug-bar ul.tracy-previous li {
	opacity: .6;
	background: #F5F3EE;
}

#tracy-debug-bar ul.tracy-previous li:first-child {
	width: 4.1em;
	text-align: right;
}

#tracy-debug-bar img {
	vertical-align: bottom;
	position: relative;
	top: -2px;
}

#tracy-debug-bar svg {
	vertical-align: bottom;
	width: 1.23em;
	height: 1.55em;
}

#tracy-debug-bar .tracy-label {
	margin-left: .2em;
}

#tracy-debug-bar li > a,
#tracy-debug-bar li > span {
	color: #000;
	display: block;
	padding: 0 .4em;
}

#tracy-debug-bar li > a:hover {
	color: black;
	background: #c3c1b8;
}

#tracy-debug-logo {
	cursor: move;
}

#tracy-debug-logo svg {
	width: 3.4em;
	margin: 0 .2em 0 .5em;
}


/* panels */
#tracy-debug .tracy-panel {
	font: normal normal 12px/1.5 sans-serif;
	background: white;
	color: #333;
	text-align: left;
	z-index: 20001;
}

#tracy-debug h1 {
	font: normal normal 23px/1.4 Tahoma, sans-serif;
	color: #575753;
	margin: -5px -5px 5px;
	padding: 0 25px 5px 5px;
	max-width: 700px;
	word-wrap: break-word;
}

#tracy-debug .tracy-mode-peek .tracy-inner,
#tracy-debug .tracy-mode-float .tracy-inner {
	max-width: 700px;
	max-height: 500px;
	overflow: auto;
}

#tracy-debug .tracy-panel .tracy-icons {
	display: none;
}

#tracy-debug .tracy-mode-peek,
#tracy-debug .tracy-mode-float {
	position: fixed;
	right: 0;
	bottom: 0;
	padding: 10px;
	min-width: 150px;
	min-height: 50px;
	border-radius: 5px;
	box-shadow: 1px 1px 20px rgba(102, 102, 102, 0.36);
	border: 1px solid rgba(0, 0, 0, 0.1);
}

#tracy-debug .tracy-mode-peek {
	display: none;
}

#tracy-debug .tracy-mode-peek h1 {
	cursor: move;
}

#tracy-debug .tracy-mode-float {
	opacity: .95;
	transition: opacity 0.2s;
}

#tracy-debug .tracy-focused {
	opacity: 1;
	transition: opacity 0.1s;
}

#tracy-debug .tracy-mode-float h1 {
	cursor: move;
}

#tracy-debug .tracy-mode-float .tracy-icons {
	display: block;
	position: absolute;
	top: 0;
	right: 5px;
	font-size: 18px;
}

#tracy-debug .tracy-icons a {
	color: #575753;
}

#tracy-debug .tracy-icons a:hover {
	color: white;
}


/* dump */
#tracy-debug pre.tracy-dump div {
	padding-left: 3ex;
}

#tracy-debug pre.tracy-dump div div {
	border-left: 1px solid rgba(0, 0, 0, .1);
	margin-left: .5ex;
}

#tracy-debug pre.tracy-dump {
	background: #FDF5CE;
	padding: .4em .7em;
	border: 1px dotted silver;
	overflow: auto;
}

#tracy-debug table pre.tracy-dump {
	padding: 0;
	margin: 0;
	border: none;
}

#tracy-debug .tracy-dump-array,
#tracy-debug .tracy-dump-object {
	color: #C22;
}

#tracy-debug .tracy-dump-string {
	color: #35D;
}

#tracy-debug .tracy-dump-number {
	color: #090;
}

#tracy-debug .tracy-dump-null,
#tracy-debug .tracy-dump-bool {
	color: #850;
}

#tracy-debug .tracy-dump-visibility,
#tracy-debug .tracy-dump-hash {
	font-size: 85%; color: #999;
}

#tracy-debug .tracy-dump-indent {
	display: none;
}


@media print {
	#tracy-debug * {
		display: none;
	}
}
