<?php

/**
 * Debug Bar: panel "dumps" template.
 *
 * This file is part of the Tracy (https://tracy.nette.org)
 * Copyright (c) 2004 <PERSON> (https://davidgrudl.com)
 */

namespace <PERSON>;

use <PERSON>;

?>
<style class="tracy-debug">#tracy-debug .tracy-DumpPanel h2{font:11pt/1.5 sans-serif;margin:0;padding:2px 8px;background:#3484d2;color:white}</style>

<h1>Dumps</h1>

<div class="tracy-inner tracy-DumpPanel">
<?php foreach ($data as $item): ?>
	<?php if ($item['title']):?>
	<h2><?php echo htmlspecialchars($item['title'], ENT_NOQUOTES, 'UTF-8') ?></h2>
	<?php endif ?>

	<?php echo $item['dump'] ?>
<?php endforeach ?>
</div>
