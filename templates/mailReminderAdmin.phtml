<strong><PERSON><PERSON><PERSON><PERSON><PERSON> o od<PERSON><PERSON><PERSON><PERSON> následu<PERSON><PERSON><PERSON><PERSON><PERSON>:</strong><br><br>
{foreach $debtors15 as $row}
  <p><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON>: {$row->usrname}<br>
  <PERSON><PERSON><PERSON>: {foreach $row["numbers"] as $rown}{$rown->numnumber}{if !$iterator->isLast()}, {/if}{/foreach}</p>
{/foreach}
</p>


<strong><PERSON><PERSON><PERSON><PERSON><PERSON> o s<PERSON><PERSON><PERSON><PERSON> následují<PERSON><PERSON>ch č<PERSON>el:</strong><br><br>
{foreach $debtors30 as $row}
  <p><PERSON><PERSON><PERSON>, příjmení: {$row->usrname}<br>
  <PERSON><PERSON><PERSON>: {foreach $row["numbers"] as $rown}{$rown->numnumber}{if !$iterator->isLast()}, {/if}{/foreach}</p>
{/foreach}
{include 'mailFooter.phtml'}