{* loader helperů *}
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xml:lang="cs" lang="cs">
<head>
  <title>ADMINISTRACE {$presenter->config["SERVER_NAME"]} - {block #title}{/block}</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="Content-Language" content="cs" />
  <meta name="robots" content="nofollow,noindex" />
  <link rel="stylesheet" media="screen,projection,tv" href="{$basePath}/css/admin.css" type="text/css">  
{control js
  'jquery.js',
  'jquery-ui.js',
  'netteForms.js', 
  'web.js'
}
{control texylaJs}
{control css 
  WWW_DIR.'/css/jquery-ui/jquery-ui.css',
  WWW_DIR.'/texyla/css/style.css',
  WWW_DIR.'/texyla/themes/default/theme.css'
}
</head>                   

<body style="background-color: {if $sec == 'call'}#008000{else}#8080FF{/if};">
  <div id="content"> 
	  {*horni menu*}
    {* menu se zobrazi jen prihlasenym *}
    {if $admin->admid > 0} 
    <h1>{if $sec == 'call'}Volání{else}Internet{/if}</h1>
    <div id="menu-top">
      {if $sec == 'call'}
        <span style="background-color: #8080FF;"><a href="{$baseUri}/administrace/?changeSec=wifi">Přepnout na internet</a></span>
      {else}
        <span style="background-color: #008000;"><a href="{$baseUri}/administrace/?changeSec=call">Přepnout na volání</a></span>
      {/if}
      <span><a {ifCurrent Admin:default}class="current"{/ifCurrent} href="{plink Admin:default}">Úvodní stránka</a></span>
      <span><a {ifCurrent User:*}class="current"{/ifCurrent} href="{plink User:default 'h'=>0}">Klienti</a></span>
      {if $sec == 'call'}
      <span><a {ifCurrent Pricelist:*}class="current"{/ifCurrent} href="{plink Pricelist:default}">Ceníky</a></span>
      <span><a {ifCurrent Import:*}class="current"{/ifCurrent} href="{plink Import:default}">Import</a></span>
      {/if}
      <span><a {ifCurrent Invoice:*}class="current"{/ifCurrent} href="{plink Invoice:default}">Faktury {if $sec == 'call'}volání{else}internet{/if}</a></span>
      <span><a {ifCurrent Invoice:*}class="current"{/ifCurrent} href="{plink Invoice:others}">Faktury ostatní</a></span>
      <span><a {ifCurrent Page:*}class="current"{/ifCurrent} href="{plink Page:default}">Stránky</a></span>
      <span><a {ifCurrent Contract:*}class="current"{/ifCurrent} href="{plink Contract:default}">Smlouvy</a></span>
      <span><a {ifCurrent Admin:Config}class="current"{/ifCurrent} href="{plink Config:default}">Nastavení</a></span>
      <span><a {ifCurrent Page:sheet}class="current"{/ifCurrent} href="{plink Page:sheet}">Poznámky</a></span>
      <span><a href="{plink Admin:logout}">Odhlásit se</a></span>

    </div>
    
    {ifCurrent User:*}
    <br />
    <div id="submenu">  
      <span><a {ifCurrent User:edit}class="current"{/ifCurrent} href="{plink User:edit, 0}">Nový klient</a></span>
      <span><a {ifCurrent User:default 'sH'=>0}class="current"{/ifCurrent} href="{plink User:default 'sH'=>0}">Výpis aktivních</a></span>
      <span><a {ifCurrent User:default 'sH'=>1}class="current"{/ifCurrent} href="{plink User:default 'sH'=>1}">Výpis blokovaných</a></span>
    </div>
    {/ifCurrent}
    
    {ifCurrent Pricelist:*}
    <br />
    <div id="submenu">  
      <span><a {ifCurrent Pricelist:edit, 0}class="current"{/ifCurrent} href="{plink Pricelist:edit, 0}">Nový ceník</a></span>
      <span><a {ifCurrent Pricelist:default 'h'=>0}class="current"{/ifCurrent} href="{plink Pricelist:default 'h'=>0}">Aktivní ceníky</a></span>
      <span><a {ifCurrent Pricelist:default 'h'=>1}class="current"{/ifCurrent} href="{plink Pricelist:default 'h'=>1}">Skryté ceníky</a></span>
    </div>
    {/ifCurrent}
    
    {* submenu stranky *}
    {ifCurrent Page:*}
    <br />
    <div id="submenu">  
      <span><a {ifCurrent Page:default}class="current"{/ifCurrent} href="{plink Page:default}">Seznam textových stránek</a></span>
      <span><a {ifCurrent Page:edit, 0}class="current"{/ifCurrent} href="{plink Page:edit, 0}">Nová stránka</a></span>
    </div>
    {/ifCurrent}

      {* submenu faktury *}
      {ifCurrent Invoice:others}
        <br />
        <div id="submenu">
          <span><a {ifCurrent Invoice:others}class="current"{/ifCurrent} href="{plink Invoice:others}">Seznam faktur - ostatní</a></span>
          <span><a {ifCurrent Invoice:add}class="current"{/ifCurrent} href="{plink Invoice:add}">Nová faktura</a></span>
        </div>
      {/ifCurrent}

    {* submenu smlouvy *}
    {ifCurrent Contract:*}
    <br />
    <div id="submenu">  
      <span><a {ifCurrent Contract:default}class="current"{/ifCurrent} href="{plink Contract:default}">Seznam smluv</a></span>
      <span><a {ifCurrent Contract:edit, 0}class="current"{/ifCurrent} href="{plink Contract:edit, 0}">Nová verze smlouvy/dodatek</a></span>
    </div>
    {/ifCurrent}
    
    {/if}  

    {*info box*}
    {foreach $flashes as $flash}<div class="flash {$flash->type}">{!$flash->message}</div>{/foreach}
  
    {*obsah*}
    {include #content} 
    
    {*paticka*}
    <div id="footer">
      <a href="{plink :Front:Homepage:default}">Veřejná část</a> |
      {if $admin->admid > 0} 
      Přihlášen/a {$admin->admname} ({$admin->admmail})
      {else}
      Nepřihlášen/a
      {/if}
    </div>
  </div>
</body>
</html>