{block #content}
<h3>{block #title}Úvodní stránka administrace{/block}</h3>

<h4>Hledání neuhrazené výzvy k platbě</h4>  
{control searchNotPayedInvoiceForm}
{ifset $invRows}
{foreach $invRows as $row}
  {if $iterator->isFirst()}
    <h4>Nalezené neuhrazené výzvy k platbě</h4>
    <p><strong>Hledáno:</strong> {$filterText}</p>
    <table class="grid">
    <tr>
      <th></th>
      <th>Var.sym.</th>
      <th>Dní po splatnosti</th>
      <th>Částka</th>
      <th colspan="4">Adresa</th>
      <th>Telefon</th>
      <th>Mail</th>
      <th>&nbsp;</th>
      <th>&nbsp;</th>
      <th>&nbsp;</th>
    </tr>
    {/if}
    <tr>
      <td><img src="{$baseUri}/ico/{$row->invtype}.png" width="16" height="16" /></td>
      <td>{$row->invvarsym}</td>
      <td>{$row->invduedateafter}</td>
      <td style="text-align: right;">{$row->invpricevat|number:2:',':' '}</td>
      <td>{$row->invrow1}</td>
      <td>{$row->invrow2}</td>
      <td>{$row->invrow3}</td>
      <td>{$row->invrow4}</td>
      <td>{$row->invphone}</td>
      <td>{$row->invmail}</td>
      <td>
      <a href="{plink Invoice:getInvoice $row->invid, 'D'}" target="pdf"><img src="{$baseUri}/ico/pdf.png" width="16" height="16" alt="export do PDF" title="Stáhnout PDF" /></a>
      <a href="{plink Invoice:getInvoice $row->invid, 'I'}" target="pdf"><img src="{$baseUri}/ico/magnifier.png" width="16" height="16" alt="otevřít PDF" title="otevřít PDF" /></a>
      </td>
      <td><a href="{plink Invoice:payInvoice $row->invid}">UHRADIT PŘEVODEM</a></td>
      <td><a href="{plink Invoice:payInvoice $row->invid, true}">UHRADIT HOTOVĚ</a></td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
{/foreach}
{if count($invRows) == 0} 
<p>Nic jsem nenašel</p>
{/if}
{/ifset}

<h4>Pohledávky starší 15 dní po klientech</h4>
{var $sum=0; $cnt=0;}
{foreach $notPayedAfter as $row}
  <?php  
  $sum += (int)$row->invpricevat; 
  $cnt += (int)$row->cnt; 
  ?>
  {if $iterator->isFirst()}
  <table class="grid">
  <tr>
    <th>Pořadí</th>
    <th>Jméno</th>
    <th>Mobil</th>
    <th>Počet pohledávek</th>
    <th>Dluh celkem</th>
    <th>Nejstarší pohl.</th>
    <th></th>
  </tr>
  {/if}
  <tr>
    <td>{$iterator->getCounter()}</td>
    <td><a href="{plink "User:edit", $row->usrid}">{$row->usrname}</a></td>
    <td>{$row->usrphone}</td>
    <td style="text-align: right;">{$row->cnt}</td>
    <td style="text-align: right;">{$row->invpricevat|number:0:',':' '} Kč</td>
    <td style="text-align: right;">{$row->invduedateafter} dní</td>
    <td><a href="{plink User:edit $row->usrid}"><img src="{$baseUri}/ico/user.png" width="16" height="16" alt="editace klienta" title="editace klienta" /></a></td>
  </tr>
  {if $iterator->isLast()}
  <tr>
    <th></th>
    <th>Celkem</th>
    <th></th>
    <th style="text-align: right;">{$cnt}</th>
    <th style="text-align: right;">{$sum|number:0:',':' '} Kč</th>
    <th></th>
    <th></th>
  </tr>
  </table>
  {/if}
{/foreach}
{if count($notPayedAfter) == 0}Žádné pohledávky starší 15 dní{/if}


<h4>Aktuální požadavky na změnu</h4>
  {ifset $tacs}
{foreach $tacs as $row}
  {if $iterator->isFirst()}
  <table class="grid">
  <tr>
    <th>Tarif</th>
    <th>Jméno</th>
    <th>Číslo</th>
    <th>Datum podání</th>
    <th></th>
  </tr>
  {/if}
  <tr>
    <td>{$enum_pliid[$row->tacpliid]}</td>
    <td><a href="{plink "User:edit", $row->usrid}">{$row->usrname}</a></td>
    <td>{$row->tacnumber}</td>
    <td>{$row->tacdatec|date:'d.m.Y'}</td>
    <td><a href="{plink User:tacFinshed, $row->tacid}">Změnit tarif nyní</a></td>
  </tr>
  {if $iterator->isLast()}
  </table>
  {/if}
{/foreach}
{if count($tacs) == 0}Žádné požadavky{/if}
    {/ifset}
{/block}
