{block #content}
  <h3>{block #title}Klienti{/block}</h3>
  {form searchForm}
  <fieldset>
  <legend><PERSON><PERSON><PERSON><PERSON>ávání</legend>
  {label name /}: {input name}
  {label phone /}: {input phone}
  {label paytypid /}: {input paytypid}
  {input h} {label h /}
  {input np} {label np /}
  {input search}
  {input clear}
  </fieldset>
  {/form}
  <form method="post" action="{plink User:batchAction}"> 
  <table class="grid">
  <tr>
    <th><input type="checkbox" id="checkAll" title="Zaškrtnout/odškrtnou vše"></th>
    <th>Jméno</th>
    <th>Email</th>
    <th>Telefon</th>
    <th>Status</th>
    <th></th>
  </tr>
{foreach $dataRows as $row}
    <tr>
      <td><input class="usrid_chk" type="checkbox" name="usrid[{$row->usrid}]" value="{$row->usrid}" ></td>
      <td>{$row->usrname}</td>
      <td>{$row->usrmail}</td>
      <td>{$row->usrphone}</td>
      <td>{$enum_usrstatus[$row->usrstatus]}</a></td>
      <td><a href="{plink User:edit, $row->usrid}"><img src="{$baseUri}/ico/edit.png" width="16" height="16" /></a></td>
    </tr>
    {if isset($notPayedByClient[$row->usrid]) && count($notPayedByClient[$row->usrid]) > 0}
      <tr><td colspan="8">Neuhrazené pohledávky:<table class="grid" style="padding:0px;margin:0px">
      {foreach $notPayedByClient[$row->usrid] as $inv}
      <tr>
      <td>
        {if empty($inv->invcode)}
          {$inv->invvarsym}
        {else}
          {if !empty($inv->invvyfcode)}
            {$inv->invvyfcode}
          {else}
            {$inv->invcode}
          {/if}
        {/if}
      </td>
      <td>{$inv->invpricevat} Kč</td>
      <td>{$inv->invpaydate|date:'d.m.Y'}</td>
      </tr>
      {/foreach}
      </table></td></tr>
    {/if}

{/foreach}
  </table>
  <p><input type="submit" name="maillist" value="Mailovat zaškrtnutým"> &nbsp; <input type="submit" name="print" value="Tisknout sestavu"></p>
  </form>
  <script type="">
    $("#checkAll").click(function(){
      $('.usrid_chk').not(this).prop('checked', this.checked);
    });
  </script>  
{/block}