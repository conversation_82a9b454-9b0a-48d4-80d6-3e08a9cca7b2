{block #content}
  <script>
  $(function() {
    $("#tabs").tabs();
  });
  </script>
  <h3>{block #title}{ifset $dataRow}Klient {$dataRow->usrmail}{else}Nový klient{/ifset} {/block}</h3>  

  <div id="tabs">
    <ul>  
      <li><a href="#tabs-edit">Editace</a></li>
      {if $sec == 'call'}<li><a href="#tabs-numbers">Telefonní čísla</a></li>{/if}
      <li><a href="#tabs-inv">Vystavené doklady</a></li>
      {if $sec == 'call'}<li><a href="#tabs-contract">Smlouva</a></li>{/if}
    </ul>
  
  <div id="tabs-inv">
  {ifset $invoices}
    <h2>Vystavené doklady</h2>
    {foreach $invoices as $row}
      {if $iterator->isFirst()}
      <table class="grid">
      <tr>
        <th></th>
        <th></th>
        <th>číslo</th>
        <th>rok</th>
        <th>měs<PERSON><PERSON></th>
        <th>částka</th>
        <th>splatnost</th>
        <th>po splatnosti</th>
        <th>datum úhrady</th>
        <th>&nbsp;</th>
      </tr>
      {/if}
      <tr>
        <td><img src="{$baseUri}/ico/{$row->invtype}.png" width="16" height="16" /></td>
        <td>{if empty($row->invcode)} výzva k platbě {else}faktura{/if}</td>
        <td>
          {if empty($row->invcode)}
            {$row->invvarsym}
          {else}
            {if !empty($row->invvyfcode)}
              <a href="{$row->invvyfurl}" target="_blank">{$row->invvyfcode}</a>
            {else}
              {$row->invcode}
            {/if}
          {/if}
        </td>
        <td>{$row->invyear}</td>
        <td>{$row->invmonth}</td>
        <td>{$row->invpricevat}</td>
        <td>{$row->invpaydate|date:'d.m.Y'}</td>
        <td>{$row->invduedateafter} dní</td>
        <td>{if !empty($row->invpayeddate)}{$row->invpayeddate|date:'d.m.Y'}{else}{if !empty($row->invcode) || (empty($row->invcode) && $row->usrpayrateid=0)}<a href="{plink Invoice:payInvoice $row->invid}">ZAPLATIT</a>{/if}{/if}</td>
        <td>
          <a href="{plink Invoice:getInvoice $row->invid, 'D'}"><img src="{$baseUri}/ico/pdf.png" width="16" height="16" alt="export do PDF" title="Stáhnout PDF" /></a>
          <a href="{plink Invoice:getInvoice $row->invid, 'I'}"><img src="{$baseUri}/ico/magnifier.png" width="16" height="16" alt="otevřít PDF" title="otevřít PDF" /></a>

          {if !empty($row->invvyfurl)}
            <a href="{$row->invvyfurl}" target="_blank"><img src="{$baseUri}/ico/front.png" width="16" height="16" alt="faktura na vyfakturuj.cz" title="faktura na vyfakturuj.cz" target="_blank" /></a>
          {/if}

          <a href="{plink Invoice:edit $row->invid}"><img src="{$baseUri}/ico/edit.png" width="16" height="16" alt="upravit" title="upravit" /></a>
        </td>
      </tr>
      {if $iterator->isLast()}
      </table>
      {/if}
    {/foreach}
  {/ifset}
  </div>
  <div id="tabs-edit">
  {control userEditForm}
  </div>
  {if $sec == 'call'}
  <div id="tabs-numbers">
  <h3>Přidělená čísla</h3>
  {form userNumbersEditForm}
    <?php $form->render('errors') ?>
  <!-- vykreslení chyb -->                          
  <table class="grid">
    <tr>
      <th>Nové Číslo</th>
      <th>Tarif</th>
      <th>Od</th>
      <th>Do</th>
      <th></th>    
    </tr>
    <tr>
      <td><?php echo $form['data'][0]["numnumber"]->control ?></td>
      <td><?php echo $form['data'][0]["numpliid"]->control ?></td>
      <td><?php echo $form['data'][0]["numdatefrom"]->control ?></td>
      <td><?php echo $form['data'][0]["numdateto"]->control ?></td>
      <td></td>
    </tr>
    <tr>
      <th>Číslo</th>
      <th>Tarif</th>
      <th>Od</th>
      <th>Do</th>
      <th></th>    
    </tr>
    {ifset $dataRow}
    {foreach $numbers as $row} 
    <tr>
      <td><?php echo $form['data'][$row->numid]["numnumber"]->control ?></td>
      <td><?php echo $form['data'][$row->numid]["numpliid"]->control ?></td>
      <td><?php echo $form['data'][$row->numid]["numdatefrom"]->control ?></td>
      <td><?php echo $form['data'][$row->numid]["numdateto"]->control ?></td>
      <td><a href="{plink User:deleteNumber, $row->numid, $row->numusrid}"><img src="{$baseUri}/ico/delete.png" width="16" height="16" /></a></td>
    {/foreach}
    {/ifset}
    </tr>
  </table>  
  
  {input save}
  {/form}
  
  <h4>Změna tarifu</h4>
  {control changeTarifForm}
  </div>
  
  
  <div id="tabs-contract">
  {ifset $dataRow}
  <h3>Smlouva</h3>
  <p>
    <a href="{plink User:getContract, $dataRow->usrid, 'D'}">Stáhnout PDF</a> |
    <a href="{plink User:getContract, $dataRow->usrid, 'I'}">otevřít PDF</a>
  </p>
  
  {form userContractsEditForm}
  <!-- vykreslení chyb -->                          
  {if count($contracts) > 0} 
  <table class="grid">
    <tr>
      <th>Verze smlouvy/dodatku</th>
      <th>Datum vytvoření</th>
      <th>Datum podpisu</th>
      <th></th>    
    </tr>
    {foreach $contracts as $row}
    <tr>
      <td>{$row->conname}</td>
      <td>{$row->coldatec|date:'d.m.Y'}</td>
    {ifset $form['data'][$row->colid]} 
      <td><?php echo $form['data'][$row->colid]["coldatesigned"]->control ?></td>
    {else}
      <td>{$dataRow->coldatesigned|date:'d.m.Y'}</td>
    {/ifset}
      <td><a href="#{*plink User:deleteContract, $row->colid, $row->colusrid*}"><img src="{$baseUri}/ico/delete.png" width="16" height="16" /></a></td>
    </tr>  
    {/foreach}
  </table>  
  {/if}
  <p>{input save}</p>
  {/form}
  {/ifset}
</div>
{/if}



{ifset $dataRow}
    <p><a href="{plink User:delete, $dataRow->usrid}" onclick="return DeleteConfirm('klienta {!$dataRow->usrname}');"><img src="{$baseUri}/ico/delete.png" width="16" height="16" /> VYMAZAT ÚČET </a></p>
  {/ifset}
{/block}
