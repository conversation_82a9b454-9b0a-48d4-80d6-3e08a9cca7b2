{block #content}
  <h3>{block #title}Ceníky{if !empty($presenter->serid)} - {$presenter->serid}{else} - všechny{/if}{/block}</h3>

  <p>V<PERSON><PERSON> jen <a href="{plink this serid=>'tmobile'}">T-Mobile</a> | <a href="{plink this serid=>'o2'}">O2</a> | <a href="{plink this serid=>'vodafone'}">Vodafone</a> | <a href="{plink this serid=>'tmobile2'}">Tmobile 2</a>  | <a href="{plink this serid=>NULL}">VŠE</a> </p>
  <table class="grid">
  <tr>
    <th>Poskytovatel</th>
    <th>Název</th>
    <th>Pa<PERSON><PERSON><PERSON>l</th>
    <th>Volné minuty</th>
    <th>Volné SMS</th>
    <th>Volné <PERSON></th>
    <th>Status</th>
    <th></th>
  </tr>
{foreach $dataRows as $row}
    <tr>
      <td>{$row->pliserid}</td>
      <td>{$row->pliname}</td>
      <td>{$row->plifee}</td>
      <td>{$row->plifreemin}</td>
      <td>{$row->plifreesms}</td>
      <td>{$row->plifreemms}</td>
      <td>{$enum_plistatus[$row->plistatus]}</a></td>
      <td><a href="{plink Pricelist:edit, $row->pliid}"><img src="{$baseUri}/ico/edit.png" width="16" height="16" /></a>&nbsp;<a href="{plink Pricelist:delete, $row->pliid}"  onclick="return DeleteConfirm('ceník {!$row->pliname}');"><img src="{$baseUri}/ico/delete.png" width="16" height="16" /></a></td>
    </tr>
{/foreach}
  </table>  
{/block}