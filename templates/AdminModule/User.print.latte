{layout NULL}

{block #content}
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xml:lang="cs" lang="cs">
<head>
  <title>ADMINISTRACE <PERSON>, Miseron Dex s.r.o. - K<PERSON><PERSON> pohled<PERSON>vky </title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<meta http-equiv="Content-Language" content="cs">
  <meta name="robots" content="nofollow,noindex">

  <style>
    table.grid {
      padding: 10px;
      margin: 10x;
      border-collapse:collapse;
    }

    table.grid td, table.grid th {
      border: 1px solid black;
      padding: 2px 5px 2px 5px;
    }

    table.grid th {
      background: silver ;
      text-align: left;
    }

    table.grid .alt td {
      background: #f8f8f0;
    }
  </style>
</head>
<body>
  <h3>{block #title}Klienti{/block}</h3>

  <table class="grid">
    {*<tr>
    <th>Jméno</th>
    <th>Email</th>
    <th>Telefon</th>
    <th>Způsob úhrady</th>
  </tr>*}
  {foreach $users as $row}
    <tr>
      <th>{$row->usrname}</th>
      <th>{$row->usrmail}</th>
      <th>tel: {$row->usrphone}</th>
      <th>{$enum_usrpaytypid[$row->usrpaytypid]}</th>
    </tr>
    <tr>
      <td colspan="8">
        {if isset($notPayedByClient[$row->usrid]) && count($notPayedByClient[$row->usrid]) > 0}
        Neuhrazené pohledávky:
        <table class="grid" style="padding:0px;margin:0px">
          {foreach $notPayedByClient[$row->usrid] as $inv}
          <tr>
            <td><input type="checkbox"></td>
            <td>{if empty($inv->invcode)}{$inv->invvarsym}{else}{$inv->invcode}{/if}</td>
            <td>{$inv->invpricevat} Kč</td>
            <td>{$inv->invpaydate|date:'d.m.Y'}</td>
          </tr>
          {/foreach}
        </table>
        {else}
        Vše uhrazeno
        {/if}
      </td>
    </tr>

{/foreach}
  </table>

</body>
</html>
{/block}