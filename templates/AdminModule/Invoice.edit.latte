{block #content}
  <h3>{block #title} Editace faktury č. {$dataRow->invcode} {/block}<br>typ faktury: {$enum_invtype[$dataRow->invtype]}</h3>
  {if $dataRow->invstatus == 1}<div class="flash err">POZOR! Tato faktura je již uhrazena.</div>{/if}
  <p>
    <a href="{plink getInvoice $dataRow->invid, 'D'}"><img src="{$baseUri}/ico/pdf.png" width="16" height="16" alt="export do PDF" title="Stáhnout PDF" /></a> |
    <a href="{plink getInvoice $dataRow->invid, 'I'}" target="PDF"><img src="{$baseUri}/ico/magnifier.png" width="16" height="16" alt="otevřít PDF" title="otevřít PDF" /></a> |
    {if empty($dataRow->invpayeddate)}
      {if $dataRow->invpaytypid == 2}
        <a href="{plink Invoice:payInvoice $dataRow->invid, true}">UHRADIT HOTOVĚ{if $user->usrpayrateid > 0} VČETNĚ EET{/if}</a> |
        {if empty($dataRow->inveetfik) && $user->usrpayrateid > 0}
          <a href="{plink Invoice:EetSend $dataRow->invid}">JEN ODESLAT DO EET</a>
        {elseif !empty($dataRow->inveetfik)}
          FIK: {$dataRow->inveetfik} |
        {/if}
      {else}
        <a href="{plink Invoice:payInvoice $dataRow->invid}">UHRADIT PŘEVODEM</a>
      {/if}
    {/if}
  </p>

  {control editForm}

{if $dataRow->invtype == 'call'}
  {form callItemsEditForm}
  
  <table class="grid">
    <tr>
      <th>Číslo</th>
      <th>DPH</th>
      <th>Jednotka</th>
      <th>Cena</th>
      <th>Množství</th>
      <th>Volné množství</th>
      <th>Cena celkem</th>
      <th>Text</th>
      <th></th>    
    </tr>
    {foreach $form['items']->getComponents() as $id => $item}
    {if $id == 0}
      <tr>
        <th colspan="10">Nová položka</th>
      </tr>
    {/if}
    <tr>
      <td>{input items-$id-nninumber}</td>
      <td>{input items-$id-nnivat}</td>
      <td>{input items-$id-nniunit}</td>
      <td>{input items-$id-nniprice}</td>
      <td>{input items-$id-nnicnt}</td>
      <td>{input items-$id-nnicntfree}</td>
      <td>{input items-$id-nnipricecnt}</td>
      <td>{input items-$id-nnitext}</td>
      <td>{if $id > 0}{input items-$id-delete}{/if}</td>
    {/foreach}
    </tr>
  </table>  
  {input save}
  {/form}
{else}
  {form itemsEditForm}

    <table class="grid">
      <tr>
        <th>Cena/kus</th>
        <th>Kusy</th>
        <th>DPH</th>
        <th>Text</th>
        <th></th>
      </tr>
      {foreach $form['items']->getComponents() as $id => $item}
      {if $id == 0}
        <tr>
          <th colspan="10">Nová položka</th>
        </tr>
      {/if}
      <tr>
        <td>{input items-$id-iniprice}</td>
        <td>{input items-$id-inicnt}</td>
        <td>{input items-$id-inivat}</td>
        <td>{input items-$id-initext}</td>
        <td>{if $id > 0}{input items-$id-delete}{/if}</td>
        {/foreach}
      </tr>
    </table>
    {input save}
  {/form}
{/if}

{if $dataRow->invtype == 'othr'}
  {if $dataRow->invstatus == 1}<div class="flash err">POZOR! Tato faktura je již uhrazena.</div>{/if}<br>
  <a href="{plink 'deleteInvoice', $dataRow->invid}" onclick="return(DeleteConfirm('fakturu č. {!$dataRow->invcode}'))">Vymazat fakturu</a>
{/if}
{if !empty($eet)}
  <h3>EET</h3>
  <strong>Datum: </strong>{$eet->logdatec|date:'d.m.Y H:i:s'}<br>
  <strong>FIK: </strong> {$eet->logfik}<br>
  <strong>BKP: </strong> {$eet->logbkp}<br>
  <strong>Id provozovny: </strong> {$eet->logprovozid}<br>
  <strong>Id pokladny: </strong> {$eet->logpoklid}<br>
  <strong>Poř. číslo: </strong> {$eet->logid}<br>
{/if}
{/block}
