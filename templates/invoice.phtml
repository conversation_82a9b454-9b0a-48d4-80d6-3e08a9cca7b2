<table> 
<tr>
{default $infoText = 'Fakturujeme Vám za'}
{default $priceText = '<PERSON><PERSON><PERSON> k úhradě'}
{if empty($invoice->invcode) && $invUser->usrpayrateid == 0}
<td class="big" style="text-align: left;">Výzva k platbě</td>
<td class="big" style="text-align: right;">Variabilní symbol: {$invoice->invvarsym}</td>
{elseif empty($invoice->invcode) && $invUser->usrpayrateid > 1}
{var $infoText = 'Výpis'}
{default $priceText = 'Celkem'}
<td class="big" style="text-align: left;">Výpis služeb - NEPLAŤTE</td>
<td class="big" style="text-align: right;"></td>
{else}
<td class="big" style="text-align: left;">FAKTURA - DAŇOVÝ DOKLAD</td>
<td class="big" style="text-align: right;">
  ČÍSLO: {$invoice->invcode}<br>
  <span class="small">Variabilní symbol: {$invoice->invvarsym}</span>
</td>
{/if}
</tr>
</table>
<table class="border"> 
<tr>
<td style="width: 100mm;" class="right_border">
<div class="big">Dodavatel:</div>
<div class="address">
{$presenter->config["INVOICE_ADR1"]}<br />
{$presenter->config["INVOICE_ADR2"]}<br />
{$presenter->config["INVOICE_ADR3"]}<br />
{$presenter->config["INVOICE_ADR4"]}<br />
<br />
IČ: {$presenter->config["INVOICE_IC"]}<br />
DIČ: {$presenter->config["INVOICE_DIC"]}<br />
</div>
</td>
<td style="width: 100mm;">
<div class="big">Odběratel:</div>
<div class="address">
{$invoice->invrow1}<br />
{$invoice->invrow2}<br />
{$invoice->invrow3}<br />
{$invoice->invrow4}<br />
<br />
IČ: {$invoice->invic}<br />
DIČ: {$invoice->invdic}<br />
</div>
</td>
</tr>
</table>
<br>
{* Platebni podminky *}
{if !empty($invoice->invcode) || $invUser->usrpayrateid == 0}
Platební podmínky:<br>
<table class="border"> 
<tr>
<td style="width: 100mm;" class="right_border">
Datum splatnosti: {$invoice->invpaydate|date:'d.m.Y'}<br>
Datum vystavení dokladu: {$invoice->invdatec|date:'d.m.Y'}<br>
{if !empty($invoice->invcode)}
Datum uskutečnění zdaň. plnění: {$invdatevat|date:'d.m.Y'}<br>
{else}
<br>
<br>
{/if}
<br>
<br>
</td>
<td style="width: 100mm;">
Způsob úhrady: {$enum_paytypid[$invoice->invpaytypid]}<br>
{if $invUser->usrpayrateid == 0}
Název banky: {$presenter->config["INVOICE_BANKNAMEW"]}<br>
Číslo účtu: {$presenter->config["INVOICE_BANKACCW"]}<br>
{else}
Název banky: {$presenter->config["INVOICE_BANKNAME"]}<br>
Číslo účtu: {$presenter->config["INVOICE_BANKACC"]}<br>
{/if}
Variabilní symbol: {$invoice->invvarsym}<br>
</td>
</tr>
</table>
{/if}
{if $invoice->invtype == 'wifi'}
<br>
{elseif $invoice->invtype == 'call'}
<p>{$infoText} využívání telekomunikačních služeb za {if !empty($invperiod)}měsíce {$invperiod}{else} měsíc {$enum_monthsNames[$invoice->invmonth]} {$invoice->invyear}{/if}</p>
{else}
<p>{$infoText}</p>
{/if}
<?php 
$sumPriceVat = 0;
$sumVat = 0;
$sumPrice = 0;
$vatSumary = array();
$lastMonth = 0;
?>
{foreach $invoiceItems as $row}
  {if $iterator->isFirst()}
    {if $invoice->invtype == 'call'}
  <table class="border">
  <tr>
    <th>&nbsp;</th>
    <th colspan="2">Kusy/sekundy</th>
    <th>&nbsp;</th>
    <th colspan="2">DPH</th>
    <th>&nbsp;</th>
  </tr>
  <tr>
    <th>Položka</th>
    <th style="width: 22mm;">volné</th>
    <th style="width: 22mm;">účtované</th>
    <th style="width: 22mm;">Cena<br />bez DPH</th>
    <th style="width: 22mm;">Sazba</th>
    <th style="width: 22mm;">Částka</th>
    <th style="width: 22mm;">Cena<br />s DPH</th>
  </tr>
    <tr>
    <td colspan="7"><hr></td>
  </tr>
    {else}
  <table class="border">
  <tr>
    <th>Položka</th>
    <th style="width: 22mm;">Cena<br />bez DPH</th>
    <th style="width: 22mm;">Sazba</th>
    <th style="width: 22mm;">DPH</th>
    <th style="width: 22mm;">Cena<br />s DPH</th>
  </tr>
    <tr>
    <td colspan="5"><hr></td>
  </tr>
    {/if}
  {/if}
  
  <?php
  $colspan = ($invoice->invtype == 'call' ? 7 : 5);
  $vatLev = (int)$row->inivat/100;
  $vat = round((double)$row->inipricecnt*$vatLev, 2);
  $sumPriceVat += (double)$row->inipricecnt+$vat;
  $sumVat += $vat;
  $sumPrice += (double)$row->inipricecnt;
  // sumarizace DPH 
  if (!isset($vatSumary[$row->inivat])) {
    $vatSumary[$row->inivat] = array();
    $vatSumary[$row->inivat]['price'] = 0;  
    $vatSumary[$row->inivat]['vat'] = 0;
    $vatSumary[$row->inivat]['pricevat'] = 0;
  }
  $vatSumary[$row->inivat]['price'] += $row->inipricecnt;
  $vatSumary[$row->inivat]['vat'] += $vat;
  $vatSumary[$row->inivat]['pricevat'] += $row->inipricecnt+$vat;
  if ($lastMonth != $row->inimonth && !empty($invperiod)) {
    ?>
  <tr>
    <td colspan="{$colspan}" style="border-bottom: 0.5px solid black;border-top: 0.5px solid black;"><strong>{$enum_monthsNames[$row->inimonth]} {$row->iniyear}</strong></td>
  </tr> 
    <?php
    $lastMonth = $row->inimonth; 
  }
  ?>
  <tr>
    <td>{$row->initext} {if !empty($row->inimonth)}{$row->inimonth}/{$row->iniyear}{/if}</td>
    {if $invoice->invtype == 'call'}
    {if $row->iniunit == 's'}
    <?php
      $cnt = (int)$row->inicnt;
      $des = $cnt % 60;
      $min = 0;
      $sec = 0;
      if ($des > 0) { 
        // ak bezo zvysku 
        $min = floor($cnt / 60);
        $sec = $cnt - ($min * 60);   
      } else { // so zvyskom
        $min = $cnt / 60; 
      }
      $inicnt = "$min:$sec";
      $inicntfree = 0;
      if ((int)$row->inicntfree > 0) $inicntfree = $row->inicntfree;
      
      $cnt = (int)$inicntfree;
      $des = $cnt % 60;
      $min = 0;
      $sec = 0;
      if ($des > 0) { 
        // ak bezo zvysku 
        $min = floor($cnt / 60);
        $sec = $cnt - ($min * 60); 
      } else { // so zvyskom
        $min = $cnt / 60; 
      }
      $inicntfree = $min.":".$sec; 
    ?>
    <td class="items">{$inicntfree}</td>
    <td class="items">{$inicnt}</td>
    {else}
    <td class="items">{$row->inicntfree|number:0:',':' '}</td>
    <td class="items">{$row->inicnt|number:0:',':' '}</td>
    {/if}
    {/if}
    <td class="items">{$row->inipricecnt|number:2:',':' '}</td>
    <td class="items">{$row->inivat}</td>
    <td class="items">{$vat|number:2:',':' '}</td>
    <td class="items">{$row->inipricecnt+$vat|number:2:',':' '}</td>
  </tr>
  {if $iterator->isLast()}
  <tr>
    <td colspan="{$colspan}"><hr></td>
  </tr>
  {if $invoice->invtype == 'call'}
  <tr style="font-weight: bold;">
    <td colspan="3"><strong>Celkem</strong></td>
    <td class="items"><strong>{$sumPrice|number:2:',':' '}</strong></td>
    <td class="items"></td>
    <td class="items"><strong>{$sumVat|number:2:',':' '}</strong></td>
    <td class="items"><strong>{$sumPriceVat|number:2:',':' '}</strong></td>
  </tr>
  {else}
  <tr style="font-weight: bold;">
    <td><strong>Celkem</strong></td>
    <td class="items"><strong>{$sumPrice|number:2:',':' '}</strong></td>
    <td class="items"></td>
    <td class="items"><strong>{$sumVat|number:2:',':' '}</strong></td>
    <td class="items"><strong>{$sumPriceVat|number:2:',':' '}</strong></td>
  </tr>
  {/if}
  </table>
  {/if}
{/foreach}
<br>
<table width="100%">
<tr>
<td width="50%">
{if !empty($eet)}
  <strong>Tržba evidovaná v běžném režimu</strong><br>
  <strong>Datum: </strong>{$eet->logdatec|date:'d.m.Y H:i:s'}<br>
  <strong>FIK: </strong> {$eet->logfik}<br>
  <strong>BKP: </strong> {$eet->logbkp}<br>
  <strong>Id provozovny: </strong> {$eet->logprovozid}<br>
  <strong>Id pokladny: </strong> {$eet->logpoklid}<br>
  <strong>Poř. číslo: </strong> {$eet->logid}<br>
{/if}
</td>

<td width="50%" style="text-align: right">
<table  class="border">
  <tr>
    <td style="width: 25mm;">Sazba</td>
    <td style="width: 25mm;">bez daně</td>
    <td style="width: 25mm;">DPH</td>
    <td style="width: 25mm;">s daní</td>
  </tr>
  {foreach $vatSumary as $key => $dphItem}
  <tr>
    <td>{$key}%</td>
    <td>{$dphItem["price"]|number:2:',':' '} Kč</td>
    <td>{$dphItem["vat"]|number:2:',':' '} Kč</td>
    <td>{$dphItem["pricevat"]|number:2:',':' '} Kč</td>
  </tr>
  {/foreach}
  
  <tr>
    <td colspan="4"><hr><br></td>
  </tr>
  <tr>
    <td style="text-align: left;"  colspan="2"><strong>Celkem:</strong></td>
    <td  style="text-align: right;" colspan="2"><strong>{$invoice->invpricevat|number:0:',':' '},00 Kč</strong></td>
  </tr>
  {if $invoice->invstatus==0}
  {var $invpricevat = $invoice->invpricevat}
  {foreach $invoicePayments as $payment}
  {if !empty($payment->invpayeddate)}
  <?php
   $invpricevat = $invpricevat - $payment->invpricevat;  
   ?>
  <tr>
    <td style="text-align: left;" colspan="3">Uhrazeno {$payment->invpayeddate|date:'d.m.Y'}<br >platba VS {$payment->invvarsym}:</td>
    <td style="text-align: right;">-{$payment->invpricevat|number:0:',':' '},00 Kč</td>
  </tr>
  {/if}
  {/foreach}
  
  <tr style=" font-size: 16px">
    <td style="text-align: left;" colspan="3"><strong>{$priceText}:</strong></td>
    <td style="text-align: right;"><strong>{$invpricevat|number:0:',':' '},00 Kč</strong></td>
  </tr>
  {if $invoice->invpaytypid == 3}
  <tr style=" font-size: 16px; color: red;">
    <td colspan="4"><strong>PLATBA BUDE PROVEDENA INKASEM Z VAŠEHO ÚČTU,<br>NEPLAŤTE!</strong></td>
  </tr>
  {/if}
  {else}
  <tr>
    <td style="text-align: left;" colspan="2">Uhrazeno {$invoice->invpayeddate|date:'d.m.Y'}<br >platba VS {$invoice->invvarsym}:</td>
    <td style="text-align: right;" colspan="2">-{$invoice->invpricevat|number:0:',':' '},00 Kč</td>
  </tr>
  <tr style=" font-size: 16px">
    <td style="text-align: left;" colspan="2"><strong>{$priceText}:</strong></td>
    <td style="text-align: right;" colspan="2"><strong>0,00 Kč</strong></td>
  </tr>
  <tr style=" font-size: 16px; color: red;">
    <td colspan="4"><strong>NEPLAŤTE, JIŽ BYLO UHRAZENO</strong></td>
  </tr>
  {/if}
</table>
</td>
</tr>
</table>
{if !empty($invoice->invcode)}
<br> 
<p align="right" style="padding-right: 20mm;"><img src="{$baseUri}/razitko.jpg" /></p>
{/if}