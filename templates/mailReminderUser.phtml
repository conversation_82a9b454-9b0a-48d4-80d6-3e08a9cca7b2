<p>
<PERSON><PERSON><PERSON><PERSON> <PERSON>,<br />
toto je <strong>{if $invoice->invduedateafter == 2}1.{else}2.{/if} UPOMÍNKA</strong> k zaplacení úhrady v<PERSON>úč<PERSON>ání {if !empty($invoice->invcode)} faktury č. {$invoice->invcode} {/if}
{if $invoice->invtype=='wifi'}
za internetové připojení za {$invoice->invmonth}. měsíc roku {$invoice->invyear}. Dodavatel <strong><PERSON></strong>, datum splatnosti: {$invoice->invpaydate|date:'d.m.Y'}.
{elseif $invoice->invtype=='call'}
T-Mobile u společnosti <strong><PERSON></strong> za {$invoice->invmonth}. měsíc roku {$invoice->invyear}, datum splatnosti: {$invoice->invpaydate|date:'d.m.Y'}.
{elseif $invoice->invtype=='othr'}
{/if}
<br />
<br />
Platbu uhraďte převodem s následujícími údaji:<br />
Variabilní symbol: {$invoice->invvarsym}<br />
Částka: {$invoice->invpricevat|number:0:',':' '} Kč<br />
{if $invoice->usrpayrateid == 0}
na účet: {$presenter->config["INVOICE_BANKACCW"]} ({$presenter->config["INVOICE_BANKNAMEW"]}) <br />
<br />
nebo osobně.<br />
{else}
na účet: {$presenter->config["INVOICE_BANKACC"]} ({$presenter->config["INVOICE_BANKNAME"]}) <br />
<br />
nebo osobně na prodejně.<br />
{/if}
<br />
Pokud jste už zaplatili převodem a přesto jste obdrželi tuto výzvu, prosím ověřte, zda jste vyplnili správně platební údaje.<br />
<br />
Děkujeme!
{if $invoice->invtype=='call'}
<br />
<strong>Pokud dluh nebude uhrazen do 15-ti dnů po splatnosti budou všechna Vaše čísla automaticky odpojena.</strong>
<br />
<strong>Pokud dluh nebude uhrazen do 30-ti dnů po splatnosti budou všechna Vaše čísla automaticky smazána.</strong>
</p>
{/if}
{include 'mailFooter.phtml'}