{default $infoText = 'zas<PERSON><PERSON>áme Vám instrukce k zaplacení úhrady vyúčtování'}
{if empty($invoice->invcode) && $invoice->usrpayrateid == 0}
{elseif empty($invoice->invcode) && $invoice->usrpayrateid > 1}
{var $infoText = 'zasíláme Vám výpis služeb'}
{default $priceText = 'Celkem'}
{else}
{/if}
<p>
Dobrý den,<br />
{$infoText}
{if $invoice->invtype=='wifi'}
za internetové připojení za {$invoice->invmonth}. měsíc roku {$invoice->invyear}. Dodavatel <strong><PERSON></strong>, datum splatnosti: {$invoice->invpaydate|date:'d.m.Y'}.
{elseif $invoice->invtype=='call'}
T-Mobile u společnosti <strong><PERSON>, <PERSON><PERSON><PERSON> Dex s.r.o.</strong>.<br />
{/if}
<br />
  {if !empty($invoice->invvyfurl)}
Vystavenou fakturu najdete zde: {$invoice->invvyfurl}<br />
  {elseif $invoice->invtype=='wifi' || $invoice->invtype=='call'}
Podrobný rozpis služeb najdete na stránkách https://www.telcall.cz. Pro přihlášení použijte Vaše telefonní číslo. Pokud ještě nemáte heslo, nechte si jej zaslat. <br />
  {/if}
<br />
{if !empty($invoice->invcode) || $invoice->usrpayrateid == 0}
{if $invoice->invpaytypid==3}
Úhrada se platí inkasem z vašeho účtu, proto prosím neplaťte. Je pouze nutné mít na vašem účtu povoleno inkaso ve prospěch našeho účtu {$presenter->config["INVOICE_BANKACC"]}.
{else}
Platbu uhraďte převodem s následujícími údaji:<br />
Variabilní symbol: {$invoice->invvarsym}<br />
Částka: {$invoice->invpricevat|number:0:',':' '} Kč<br />
Datum splatnosti: {$invoice->invpaydate|date:'d.m.Y'}<br />
{if $invoice->usrpayrateid == 0}
na účet: {$presenter->config["INVOICE_BANKACCW"]} ({$presenter->config["INVOICE_BANKNAMEW"]}) <br />
<br />
nebo osobně.<br />
{else}
na účet: {$presenter->config["INVOICE_BANKACC"]} ({$presenter->config["INVOICE_BANKNAME"]}) <br />
<br />
nebo osobně na prodejně.<br />
{/if}
{/if}
<br />
Děkujeme!
{else}
<br />
Částka: {$invoice->invpricevat|number:0:',':' '} Kč<br />
Prosím neplaťte!
{/if}
</p>
{include 'mailFooter.phtml'}