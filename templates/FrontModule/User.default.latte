{var $title = '<PERSON><PERSON><PERSON>'}
{var $robots = noindex}

{block #content}

<div class="text">
<h2><PERSON><PERSON><PERSON></h2>
<p>
<strong><PERSON><PERSON><PERSON>, příjmení:</strong> {$user->usrname}<br /> 
<strong>Email:</strong> {$user->usrmail}<br />
<strong>Telefon:</strong> {$user->usrphone} (slouž<PERSON> z<PERSON>roveň jako přilašovací jméno)<br />

<h3>Aktuální smlouva</h3>
<p>
  <a href="{plink User:getContract 'D'}">Stáhnout PDF</a> |
  <a href="{plink User:getContract 'I'}">otevřít PDF</a>
</p>
{if $user->usrpaytypid==3}
  <h3>Máte nastaven způsob úhrady inkasem</h3> 
  <p>Pokud jste tak ještě neučinili, prosím nastavte na svém účtu povolení k inkasu ve prospěch našeho úč<PERSON> <b>{$template->config["INVOICE_BANKACC"]}</b> v přiměřené výši.</p>
{/if}

<br />
<strong>Telefonní čísla:</strong> 
{foreach $numbers as $row}
  {if $iterator->isFirst()}
  <table class="grid">
  <tr>
    <th></th>
    <th>PIN 1</th>
    <th>PIN 2</th>
    <th>PUK 1</th>
    <th>PUK 2</th>
    <th>BPUK</th>
    <th colspan="2"></th>
  </tr>
  {/if}
  <tr>
    <td>{$row->numnumber|formatPhoneNumer}</td>
    <td>{$row->numpin1}</td>
    <td>{$row->numpin2}</td>
    <td>{$row->numpuk1}</td>
    <td>{$row->numpuk2}</td>
    <td>{$row->numbpuk}</td>
    <td><a href="{plink calls 'num'=>$row->numnumber}">výpisy volání</a></td>
    <td><a href="{plink editNumber, $row->numid}"><img src="{$baseUri}/ico/edit.png" width="16" height="16" /></a></td>
  </tr>
  {if $iterator->isLast()}</table>{/if}
  {/foreach}
</p>
<h3>Ceníky volání pro jednotlivá čísla</h3>
{foreach $numbers as $row}
  {if $iterator->isFirst()}
  <script>
  $(function() {
    $("#tabs").tabs();
  });
  </script>
  <div id="tabs">
    <ul>  
  {/if}
      <li><a href="#tabs-{$row->numid}">{$row->numnumber}</a></li>
  {if $iterator->isLast()}
    </ul>  
  {/if}
{/foreach}
{foreach $numbers as $row}
  <div id="tabs-{$row->numid}">
  <table style="grid">
    {var $priceList=$row->pricelist}
    <tr><td>Měsíční paušál</td><td style="text-align: right;">{$priceList->plifee|number:2:',':' '} Kč</td></tr>
    {if !empty($priceList->plitext)}
    <?php 
    $rows = explode("\n", trim($priceList->plitext));
    foreach ($rows as $row) {
      $arr = explode(":", trim($row));
    ?><tr><td>{$arr[0]}</td><td style="text-align: right;">{$arr[1]}</td></tr>
    <?php
    }
    ?>
    {else}
    <tr><td>Cena SMS místní síť [za kus]</td><td>{$priceList->plipricesms|number:2:',':' '}</td></tr>
    <tr><td>Cena SMS do cizí sítě [za kus]</td><td>{$priceList->plipricesmsfor|number:2:',':' '}</td></tr>
    <tr><td>Cena vnitrofiremní volání [za min.]</td><td>{$priceList->plipricevnitro|number:2:',':' '}</td></tr>
    <tr><td>Cena volání do místní mobilní sítě [za min.]</td><td>{$priceList->plipricemobil|number:2:',':' '}</td></tr>
    <tr><td>Cena volání ostaních mobilních sítí [za min.]</td><td>{$priceList->plipricemobilfor|number:2:',':' '}</td></tr>
    <tr><td>Cena volání na pevnou linku do místní sítě [za min.]</td><td>{$priceList->plipricepevna|number:2:',':' '}</td></tr>
    <tr><td>Cena volání na pevnou linku u cizí sítě [za min.]</td><td>{$priceList->plipricepevnafor|number:2:',':' '}</td></tr>
    {/if}
  </table>
  </div>
  {if $iterator->isLast()}
  </div>  
  {/if}
{/foreach}
<p>Uvedené ceny jsou v Kč bez DPH</p>
{if $user->usrnoinvoice == 1}
<strong>Vystavování faktur vypnuto</strong><br />
{else}
<h3>Fakturační údaje</h3>
<strong>Adresa 1. řádek:</strong> {$user->usrirow1}<br />
<strong>Adresa 2. řádek:</strong> {$user->usrirow2}<br />
<strong>Adresa 3. řádek:</strong> {$user->usrirow3}<br />
<strong>Adresa 4. řádek:</strong> {$user->usrirow4}<br />
<strong>IČ:</strong> {$user->usriic}<br />
<strong>DIČ:</strong> {$user->usridic}<br />
<strong>Splatnost:</strong> {$user->usripaydue} dní<br />
{/if} 
</p>
{*<strong>Zasílání novinek:</strong> {if $user->usrmaillist == 1}Ano{else}Ne{/if}<br />*}
</div>
{/block}
