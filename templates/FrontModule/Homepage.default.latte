{var $title = 'Úvodn<PERSON> stránka'}

{block content}
{if $user->usrid == 0}
<p>Pokud jste zde poprvé, nechte si prosím zaslat <a href="{plink User:newPassword}">nové he<PERSON>lo</a> na email, který je uveden u Vaší registrace. 
Pak můžete pokračovat v <a href="{plink User:logIn}">přihlášení</a>.</p> 

{else}
  <h3>Aktuální ceníky volání</h3>      
  <p>
  Tarify: <a href="nove_tarify_telcall.pdf">PDF soubor</a><br />
  <br />
  <i>Klikněte na příslušný odkaz pravým tlačítkem a zvolte "Uložit cíl jako ..." pro uložení na váš počí<PERSON>č, nebo klikněte levým tlačítkem pro otevření v přímo v prohlížečce.</i>
  </p>

  <h3>Aktuální smlouva</h3>
  <p>
    <a href="{plink User:getContract 'D'}">Stáhnout PDF</a> |
    <a href="{plink User:getContract 'I'}">otevřít PDF</a>
  </p>
  
  {if $user->usrpaytypid==3}
  <h3>Máte nastaven způsob úhrady inkasem</h3> 
  <p>Pokud jste tak ještě neučinili, prosím nastavte na svém účtu povolení k inkasu ve prospěch našeho účtu <b>{$template->config["INVOICE_BANKACC"]}</b> v přiměřené výši.</p>
  {/if}
{foreach $invoicesNotPayed as $row}
  {if $iterator->isFirst()}
  <h3>Neuhrazené faktury</h3>
  <table class="grid">
  <tr>
    <th colspan="2"></th>
    <th>číslo</th>
    <th>rok</th>
    <th>měsíce</th>
    <th>částka</th>
    <th>splatnost</th>
    <th>datum úhrady</th>
    <th>faktura</th>
    {if $row->invtype=='call'}<th>vyúčtování po číslech</th>{/if}
  </tr>
  {/if}
  <tr>
    <td><img src="{$baseUri}/ico/{$row->invtype}.png" width="16" height="16" /></td>
    <td>{if empty($row->invcode)} výzva k platbě {else}faktura{/if}</td>
    <td>
      {if empty($row->invcode)}
        {$row->invvarsym}
      {else}
        {if !empty($row->invvyfcode)}
          <a href="{$row->invvyfurl}" target="_blank">{$row->invvyfcode}</a>
        {else}
          {$row->invcode}
        {/if}
      {/if}
    </td>
    <td>{$row->invyear}</td>
    <td>{$row->invmonth}</td>
    <td>{$row->invpricevat}</td>
    <td>{$row->invpaydate|date:'d.m.Y'}</td>
    <td>{if !empty($row->invpayeddate)}{$row->invpayeddate|date:'d.m.Y'}{else}NEUHRAZENÁ{/if}</td>
    <td>
      {if !empty($row->invvyfurl)}
        <a href="{$row->invvyfurl}" target="_blank"><img src="{$baseUri}/ico/pdf.png" width="16" height="16" alt="faktura" title="faktura" target="_blank" /></a>
      {else}
      <a href="{plink User:getInvoice $row->invid, 'D'}"><img src="{$baseUri}/ico/pdf.png" width="16" height="16" alt="export do PDF" title="Stáhnout PDF" /></a>
      <a href="{plink User:getInvoice $row->invid, 'I'}"><img src="{$baseUri}/ico/magnifier.png" width="16" height="16" alt="otevřít PDF" title="otevřít PDF" /></a>
      {/if}
    </td>
    {if $row->invtype=='call'}<td>
      <a href="{plink User:numberitems $row->invmonth, $row->invyear}">vyúčtování po číslech</a>
    </td>{/if}
  </tr>
  {if $iterator->isLast()}
  </table>
  {/if}
{/foreach}
<h3>Požádat o změnu ceníku</h3>
{control changeTarifForm}

{/if}
{/block}