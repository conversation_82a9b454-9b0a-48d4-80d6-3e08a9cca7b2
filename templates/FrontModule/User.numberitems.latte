{var $title = 'Moje volání'}
{var $robots = noindex}

{block #content}

<div class="text">
<h2><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> po číslech {$period}</h2>
{foreach $numbers as $number}
  <?php 
$sumPriceVat = 0;
$sumVat = 0;
$sumPrice = 0;
  ?>
  {foreach $number->items as $row}
    {if $iterator->isFirst()}
<h3>{$number->numnumber}</h3>      
<table class="grid">
  <tr>
    <th>Položka</th>
    <th>Množství<br />kusy/sekundy</th>
    <th>Cena<br />bez daně</th>
    <th>Sazba<br />DPH</th>
    <th>Částka<br />DPH</th>
    <th>Cena<br />s daní</th>
  </tr>
    {/if}
    <?php 
  $vatLev = (int)$row->nnivat/100;
  $vat = round((double)$row->nnipricecnt*$vatLev, 2);
  $sumPriceVat += (double)$row->nnipricecnt+$vat;
  $sumVat += $vat;
  $sumPrice += (double)$row->nnipricecnt;
  ?>
  <tr>
    <td>{$row->nnitext}</td>
  {if $row->nniunit == 's'}
    <?php
      $cnt = (int)$row->nnicnt;
      $des = $cnt % 60;
      $min = 0;
      $sec = 0;
      if ($des > 0) { 
        // ak bezo zvysku 
        $min = floor($cnt / 60);
        $sec = $cnt - ($min * 60);   
      } else { // so zvyskom
        $min = $cnt / 60; 
      }
      $inicnt = "$min:$sec";
    ?>
    <td style="text-align: right;">{$inicnt}</td>
    {else}
    <td style="text-align: right;">{$row->nnicnt|number:0:',':' '}</td>
    {/if}
    <td style="text-align: right;">{$row->nnipricecnt|number:2:',':' '}</td>
    <td style="text-align: right;">{$row->nnivat}</td>
    <td style="text-align: right;">{$vat|number:2:',':' '}</td>
    <td style="text-align: right;">{$row->nnipricecnt+$vat|number:2:',':' '}</td>
  </tr>
  {if $iterator->isLast()}
  <tr style="font-weight: bold;">
    <th>Celkem</th>
    <th></th>
    <th style="text-align: right;">{$sumPrice|number:2:',':' '}</th>
    <th></th>
    <th style="text-align: right;">{$sumVat|number:2:',':' '}</th>
    <th style="text-align: right;">{$sumPriceVat|number:2:',':' '}</th>
  </tr>
  </table>
  {/if}
  {/foreach}
{/foreach}
{/block}