#!/bin/sh

# aktualni vetev se musi jmenovat master

# v config git-ftp musi byt nastaven user a url pro SCOPE master
##  git config git-ftp.telcall.user telcall
##  git config git-ftp.telcall.url five.mindstorm.cz

# prvni init na ftp
##  git ftp init -s telcall -P

branch_name=$(git rev-parse --abbrev-ref HEAD)

if [ $branch_name = "master" ] ; then
echo "Spusti se push aktualni vetve: '$branch_name' ..."
echo "Login: telcall"
else
echo "Aktualni vetev neni 'master' ($branch_name) ... "
exit  
fi

/C/Users/<USER>/git-ftp/git-ftp push -s telcall -P

# posunu tag www
git tag -d www
git tag www

exit 0