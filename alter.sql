ALTER TABLE `users` ADD COLUMN `usrip` VARCHAR(250) NULL COMMENT 'IP zakaznika' AFTER `usrmaillist`, ADD COLUMN `usradminurl` VARCHAR(250) NULL COMMENT 'admin URL' AFTER `usrip`;
ALTER TABLE `invoices` <PERSON>ANGE `invtype` `invtype` CHAR(4) CHARSET utf8 COLLATE utf8_unicode_ci NULL;

ALTER TABLE `users` ADD COLUMN `usrtype` CHAR(4) NOT NULL COMMENT 'typ účtu' AFTER `usrid`;
UPDATE users SET usrtype='call' WHERE EXISTS (SELECT 1 FROM numbers WHERE numusrid=usrid);
UPDATE users SET usrtype='wifi' WHERE usrtype IS NULL;

ALTER TABLE `invoices` ADD COLUMN `ordnodph` TINYINT(1) DEFAULT 0 NOT NULL AFTER `invcode`, ADD COLUMN `ordnodphrc` TINYINT(1) DEFAULT 0 NOT NULL AFTER `ordnodph`;
ALTER TABLE `invoices` ADD COLUMN `invbankacc` VARCHAR(100) NULL COMMENT 'bankovní účet pro platby' AFTER `invpaydate`;


CREATE TABLE `eet_log` (
  `logid` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `logserid` char(4) COLLATE utf8_unicode_ci NOT NULL,
  `loginvid` int(11) DEFAULT NULL COMMENT 'id fa',
  `logprovozid` int(11) DEFAULT NULL,
  `logpoklid` int(11) DEFAULT NULL,
  `loguuid` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `logfik` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `logbkp` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `logpkp` text COLLATE utf8_unicode_ci,
  `logprice` double DEFAULT NULL,
  `logmsg` text COLLATE utf8_unicode_ci,
  `logsys` text COLLATE utf8_unicode_ci,
  `logdatec` datetime DEFAULT NULL,
  `logdateu` datetime DEFAULT NULL,
  PRIMARY KEY (`logid`),
  KEY `i_invid` (`loginvid`),
  KEY `i_fik` (`logfik`)
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

ALTER TABLE `invoices` ADD COLUMN `invprovozid` INT NULL AFTER `invid`;
ALTER TABLE `invoices` ADD COLUMN `inveetfik` VARCHAR(255) NULL AFTER `invprovozid`;

-- provedeno
ALTER TABLE `invoiceitems` CHANGE `iniprice` `iniprice` DOUBLE NULL COMMENT 'cena za kus', CHANGE `inicnt` `inicnt` DOUBLE NULL;
ALTER TABLE `invoices` CHANGE `invprice` `invprice` DOUBLE NULL;
ALTER TABLE `invoices` CHANGE `invpricevat` `invpricevat` DOUBLE NULL;

alter table invoices add invvyfid int null comment 'id fa na vyfakturuj' after invinvid;
alter table invoices add invvyfurl varchar(255) null comment 'veřejná URL na detail fa' after invvyfid;

alter table pricelists add pliserid varchar(10) null after pliid;
update pricelists set pliserid='tmobile';
create index pricelists_plistatus_pliserid_index on pricelists (plistatus, pliserid);


alter table numberitems add nniserid varchar(10) null;
alter table invoices add invserid varchar(10) null after invprovozid;

update numberitems set nniserid='tmobile';
create index numberitems_nniserid_index on numberitems (nniserid);

update invoices set invserid='tmobile';
create index invoices_invserid_index on invoices (invserid);

drop index ui_numnumber on numbers;
create index ui_numnumber on numbers (numnumber);

alter table users add usrdiscount double null;

-- provedeno

