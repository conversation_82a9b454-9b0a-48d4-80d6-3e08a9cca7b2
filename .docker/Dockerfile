FROM php:5.6-apache

# Nastavení archivních repozit<PERSON><PERSON> pro Debian Stretch (PHP 5.6)
RUN echo "deb http://archive.debian.org/debian stretch main" > /etc/apt/sources.list && \
    echo "deb http://archive.debian.org/debian-security stretch/updates main" >> /etc/apt/sources.list && \
    echo 'Acquire::Check-Valid-Until "false";' > /etc/apt/apt.conf.d/99ignore-release-date

# Aktualizace bal<PERSON> a instalace závislostí
RUN apt-get -o Acquire::Check-Valid-Until=false update && \
    apt-get install -y --no-install-recommends \
        curl \
        libzip-dev \
        libicu-dev \
        build-essential \
        libssl-dev \
        zlib1g-dev \
        libpng12-dev \
        libjpeg62-turbo-dev \
        libfreetype6-dev \
        ssl-cert \
        libmcrypt-dev && \
    rm -rf /var/lib/apt/lists/*

# Povolení Apache modulů
RUN a2enmod rewrite ssl && \
    a2ensite default-ssl

# Konfigurace a instalace PHP rozšíření
RUN docker-php-ext-configure gd --with-freetype-dir=/usr/include/ --with-jpeg-dir=/usr/include/ && \
    docker-php-ext-install mysql mysqli pdo pdo_mysql opcache zip intl gd mcrypt && \
    docker-php-ext-enable pdo_mysql opcache

# Nastavení PHP konfiguračních hodnot
RUN echo "session.save_path=\"/tmp\"" >> /usr/local/etc/php/php.ini && \
    echo "session.cookie_secure=\"0\"" >> /usr/local/etc/php/php.ini && \
    echo "session.cookie_lifetime=\"1209600\"" >> /usr/local/etc/php/php.ini && \
    echo "upload_max_filesize = 100M " >> /usr/local/etc/php/php.ini && \
    echo "post_max_size = 100M " >> /usr/local/etc/php/php.ini && \
    echo "memory_limit = 2048M " >> /usr/local/etc/php/php.ini && \
    echo "max_execution_time = 240 " >> /usr/local/etc/php/php.ini

# (Nepovinné) Xdebug připraveno, zatím zakomentováno pro případ potřeby
# RUN pecl install -f xdebug && docker-php-ext-enable xdebug
# COPY php/xdebug.ini /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

# Pracovní složka kontejneru
WORKDIR /var/www/html