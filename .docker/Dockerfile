FROM php:5.6-apache

# Nastavení archivních repozitářů pro Debian Stretch
RUN echo "deb http://archive.debian.org/debian stretch main" > /etc/apt/sources.list && \
    echo "deb http://archive.debian.org/debian stretch-backports main" >> /etc/apt/sources.list && \
    echo 'Acquire::Check-Valid-Until "false";' > /etc/apt/apt.conf.d/99ignore-release-date && \
    echo 'APT::Get::AllowUnauthenticated "true";' >> /etc/apt/apt.conf.d/99ignore-release-date

# Instalace základn<PERSON>ch bal<PERSON>ů po částech
RUN apt-get -o Acquire::Check-Valid-Until=false -o APT::Get::AllowUnauthenticated=true update

# Instalace základních nástrojů
RUN apt-get install -y --no-install-recommends --allow-unauthenticated \
        curl \
        wget \
        unzip \
        ssl-cert

# Instalace vývojových knihoven
RUN apt-get install -y --no-install-recommends --allow-unauthenticated \
        libicu-dev \
        zlib1g-dev \
        libmcrypt-dev

# Povolení Apache modulů
RUN a2enmod rewrite ssl && \
    a2ensite default-ssl

# Instalace PHP rozšíření (bez GD kvůli problémům s knihovnami)
RUN docker-php-ext-install \
        mysql \
        mysqli \
        pdo \
        pdo_mysql \
        opcache \
        mcrypt \
        zip \
        intl

# Povolení rozšíření
RUN docker-php-ext-enable pdo_mysql opcache

# Vyčištění
RUN rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Nastavení PHP konfiguračních hodnot
RUN echo "session.save_path=\"/tmp\"" >> /usr/local/etc/php/php.ini && \
    echo "session.cookie_secure=\"0\"" >> /usr/local/etc/php/php.ini && \
    echo "session.cookie_lifetime=\"1209600\"" >> /usr/local/etc/php/php.ini && \
    echo "upload_max_filesize = 100M " >> /usr/local/etc/php/php.ini && \
    echo "post_max_size = 100M " >> /usr/local/etc/php/php.ini && \
    echo "memory_limit = 2048M " >> /usr/local/etc/php/php.ini && \
    echo "max_execution_time = 240 " >> /usr/local/etc/php/php.ini

# (Nepovinné) Xdebug připraveno, zatím zakomentováno pro případ potřeby
# RUN pecl install -f xdebug && docker-php-ext-enable xdebug
# COPY php/xdebug.ini /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

# Pracovní složka kontejneru
WORKDIR /var/www/html