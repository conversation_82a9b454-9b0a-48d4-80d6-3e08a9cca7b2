FROM php:5.6-apache

# <PERSON><PERSON><PERSON><PERSON><PERSON> archivn<PERSON><PERSON> repo<PERSON><PERSON> a ode<PERSON><PERSON><PERSON><PERSON> neexistuj<PERSON><PERSON><PERSON><PERSON>dr<PERSON><PERSON> (stretch-updates)
RUN sed -i 's|http://deb.debian.org/debian|http://archive.debian.org/debian|g' /etc/apt/sources.list && \
    sed -i 's|http://security.debian.org/debian-security|http://archive.debian.org/debian-security|g' /etc/apt/sources.list && \
    echo 'Acquire::Check-Valid-Until "false";' > /etc/apt/apt.conf.d/99ignore-release-date && \
    sed -i '/stretch-updates/d' /etc/apt/sources.list && \
    apt-get -o Acquire::Check-Valid-Until=false update && \
    apt-get install -y --no-install-recommends \
        curl \
        libzip-dev \
        libwebp-dev \
        libicu-dev \
        build-essential \
        libssl-dev \
        zlib1g-dev \
        libpng-dev \
        libjpeg-dev \
        libfreetype6-dev \
        ssl-cert && \
    a2enmod rewrite ssl && \
    a2ensite default-ssl && \
    docker-php-ext-configure gd --enable-gd --with-freetype --with-jpeg --with-webp && \
    docker-php-ext-install mysql mysqli pdo pdo_mysql opcache zip intl gd && \
    docker-php-ext-enable pdo_mysql opcache && \
    rm -rf /var/lib/apt/lists/*

# Nastavení PHP konfiguračních hodnot
RUN echo "session.save_path=\"/tmp\"" >> /usr/local/etc/php/php.ini && \
    echo "session.cookie_secure=\"0\"" >> /usr/local/etc/php/php.ini && \
    echo "session.cookie_lifetime=\"1209600\"" >> /usr/local/etc/php/php.ini && \
    echo "upload_max_filesize = 100M " >> /usr/local/etc/php/php.ini && \
    echo "post_max_size = 100M " >> /usr/local/etc/php/php.ini && \
    echo "memory_limit = 2048M " >> /usr/local/etc/php/php.ini && \
    echo "max_execution_time = 240 " >> /usr/local/etc/php/php.ini

# (Nepovinné) Xdebug připraveno, zatím zakomentováno pro případ potřeby
# RUN pecl install -f xdebug && docker-php-ext-enable xdebug
# COPY php/xdebug.ini /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

# Pracovní složka kontejneru
WORKDIR /var/www/html