# Jednoduchý Dockerfile pro PHP 5.6 - záložní varianta
FROM php:5.6-apache

# Povolení Apache modulů
RUN a2enmod rewrite ssl && \
    a2ensite default-ssl

# Instalace pouze základních PHP rozšíření bez externích závislostí
RUN docker-php-ext-install \
        mysql \
        mysqli \
        pdo \
        pdo_mysql \
        opcache

# Povolení rozšíření
RUN docker-php-ext-enable pdo_mysql opcache

# Nastavení PHP konfiguračních hodnot
RUN echo "session.save_path=\"/tmp\"" >> /usr/local/etc/php/php.ini && \
    echo "session.cookie_secure=\"0\"" >> /usr/local/etc/php/php.ini && \
    echo "session.cookie_lifetime=\"1209600\"" >> /usr/local/etc/php/php.ini && \
    echo "upload_max_filesize = 100M " >> /usr/local/etc/php/php.ini && \
    echo "post_max_size = 100M " >> /usr/local/etc/php/php.ini && \
    echo "memory_limit = 2048M " >> /usr/local/etc/php/php.ini && \
    echo "max_execution_time = 240 " >> /usr/local/etc/php/php.ini

# Pracovní složka kontejneru
WORKDIR /var/www/html
